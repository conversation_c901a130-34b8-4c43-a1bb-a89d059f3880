addict==2.4.0
asttokens==3.0.0
attrs==25.3.0
blinker==1.9.0
certifi==2025.7.14
charset-normalizer==3.4.2
click==8.2.1
comm==0.2.3
configargparse==1.7.1
contourpy==1.3.3
cycler==0.12.1
dash==3.1.1
decorator==5.2.1
executing==2.2.0
fastjsonschema==2.21.1
filelock==3.18.0
flask==3.1.1
fonttools==4.59.0
fsspec==2025.7.0
h5py==3.14.0
idna==3.10
importlib-metadata==8.7.0
ipython==9.4.0
ipython-pygments-lexers==1.1.1
ipywidgets==8.1.7
itsdangerous==2.2.0
jedi==0.19.2
jinja2==3.1.6
joblib==1.5.1
jsonschema==4.25.0
jsonschema-specifications==2025.4.1
jupyter-core==5.8.1
jupyterlab-widgets==3.0.15
kiwisolver==1.4.8
markupsafe==3.0.2
matplotlib==3.10.3
matplotlib-inline==0.1.7
mpmath==1.3.0
narwhals==1.48.1
nbformat==5.10.4
nest-asyncio==1.6.0
networkx==3.5
numpy==2.3.2
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu12==12.6.80
nvidia-cuda-nvrtc-cu12==12.6.77
nvidia-cuda-runtime-cu12==12.6.77
nvidia-cudnn-cu12==********
nvidia-cufft-cu12==********
nvidia-cufile-cu12==********
nvidia-curand-cu12==*********
nvidia-cusolver-cu12==********
nvidia-cusparse-cu12==********
nvidia-cusparselt-cu12==0.6.3
nvidia-nccl-cu12==2.26.2
nvidia-nvjitlink-cu12==12.6.85
nvidia-nvtx-cu12==12.6.77
open3d==0.19.0
packaging==25.0
pandas==2.3.1
parso==0.8.4
pexpect==4.9.0
pillow==11.3.0
platformdirs==4.3.8
plotly==6.2.0
prompt-toolkit==3.0.51
ptyprocess==0.7.0
pure-eval==0.2.3
pygments==2.19.2
pyparsing==3.2.3
pyquaternion==0.9.9
python-dateutil==2.9.0.post0
pytz==2025.2
pyyaml==6.0.2
referencing==0.36.2
requests==2.32.4
retrying==1.4.1
rpds-py==0.26.0
scikit-learn==1.7.1
scipy==1.16.0
setuptools==80.9.0
six==1.17.0
stack-data==0.6.3
sympy==1.14.0
threadpoolctl==3.6.0
torch==2.7.1
torchvision==0.22.1
tqdm==4.67.1
traitlets==5.14.3
triton==3.3.1
typing-extensions==4.14.1
tzdata==2025.2
urllib3==2.5.0
wcwidth==0.2.13
werkzeug==3.1.3
widgetsnbextension==4.0.14
zipp==3.23.0

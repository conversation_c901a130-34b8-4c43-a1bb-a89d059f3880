[2025-08-04 20:02:22,418::train::INFO] DDP training initialized.
[2025-08-04 20:02:22,418::train::INFO] Running with 4 GPUs.
[2025-08-04 20:02:22,418::train::INFO] {'model': 'AutoEncoder', 'latent_dim': 256, 'num_steps': 200, 'beta_1': 0.0001, 'beta_T': 0.05, 'sched_mode': 'linear', 'residual': True, 'dataset': 'Real3DAD', 'dataset_path': '/home/<USER>/llm/3DAD/data/Real3D-AD-PCD', 'category': 'airplane', 'scale_mode': 'shape_bbox', 'num_points': 4096, 'train_batch_size': 128, 'val_batch_size': 128, 'rotate': False, 'rel': False, 'logging': True, 'use_my_defects': True, 'my_defects_path': '/home/<USER>/llm/3DAD/diffusion-point-cloud/z_R3DAD/offline_defects', 'lr': 0.001, 'weight_decay': 0, 'max_grad_norm': 10, 'sched_start_epoch': 150000, 'sched_end_epoch': 300000, 'val_freq': 2000, 'device': 'cuda:0', 'max_iters': 40000, 'seed': 2020, 'log_root': 'logs_real3d-ad-DDP/my_real3d_defect_exp_20250804-200215_my_defect_method/', 'tag': 'my_defect_method'}
[2025-08-04 20:02:22,418::train::INFO] Loading datasets...
[2025-08-04 20:02:34,960::train::INFO] Building model...

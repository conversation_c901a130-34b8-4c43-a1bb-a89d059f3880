#!/usr/bin/env python
# -*- coding: utf-8 -*-
import os
import argparse
import torch
import numpy as np
import logging
import math
from tqdm import tqdm
import matplotlib.pyplot as plt
from torch.utils.tensorboard import SummaryWriter
import open3d as o3d
import random
from sklearn.decomposition import PCA

# 导入自定义模块
from models.autoencoder import AutoEncoder, reparameterize_gaussian
from utils.dataset import Real3DADDataset
from utils.misc import get_logger, seed_all, get_new_log_dir, str_list
from utils.data import custom_collate_fn, DataLoader
from utils.transform import PCARotate

try:
    from evaluation.evaluation_metrics import distChamfer, EMD_CD
except ImportError:
    print("无法导入'distChamfer'或'EMD_CD'，指标计算可能受影响。")
    distChamfer = None
    EMD_CD = None

# 常量定义
THOUSAND = 1000

# 解析命令行参数
parser = argparse.ArgumentParser(description='生成带有缺陷的3D点云样本')
# 模型参数
parser.add_argument('--point_dim', type=int, default=3)
parser.add_argument('--residual', type=eval, default=True, choices=[True, False])
parser.add_argument('--num_points', type=int, default=2048)
parser.add_argument('--checkpoint_path', type=str, 
                   default='/home/<USER>/llm/3DAD/diffusion-point-cloud/logs_ae_vae/AE_VAE_2025_05_12__01_13_44/model_180000.pt',
                   help='预训练模型的路径')
parser.add_argument('--latent_dim', type=int, default=256, help='潜在空间的维度')
parser.add_argument('--use_vae', type=eval, default=True, choices=[True, False], help='加载的模型是否是VAE')

# 数据集参数
parser.add_argument('--dataset_path', type=str, default='/home/<USER>/llm/3DAD/data/Real3D-AD-PCD', 
                   help='数据集路径，用于读取正常样本作为缺陷生成的基础')
parser.add_argument('--categories', type=str_list, default=['airplane'], 
                   help='要生成缺陷的类别(例如,["airplane", "car"])。使用"all"表示所有可用类别')
parser.add_argument('--dataset_split', type=str, default='train', choices=['train', 'val', 'test'], 
                   help='用于生成的数据集分割')
parser.add_argument('--scale_mode', type=str, default='shape_bbox', help='点云归一化模式，需与训练时一致')
parser.add_argument('--sampling_method', type=str, default='hybrid', help='点云采样方法，需与训练时一致')
parser.add_argument('--pca_align_eval', type=eval, default=False, choices=[True, False], 
                   help='是否对评估数据应用PCA对齐')

# 生成参数
parser.add_argument('--num_samples', type=int, default=1, help='要生成的点云样本数量')
parser.add_argument('--batch_size', type=int, default=1)
parser.add_argument('--num_workers', type=int, default=4, help='DataLoader的工作线程数')
parser.add_argument('--generate_mode', type=str, default='from_dataset',
                   choices=['from_dataset', 'random_latent'],
                   help='生成模式：从数据集生成缺陷或从随机潜在向量生成')

# 缺陷生成参数
parser.add_argument('--defect_type', type=str, default='bump', 
                   choices=['cut', 'bump', 'dent', 'random', 'all'], 
                   help='要生成的缺陷类型: cut(切口), bump(凸起), dent(凹陷), random(随机选择), all(生成所有类型)')
parser.add_argument('--defect_severity', type=float, default=0, help='缺陷严重程度(扰动强度)')
parser.add_argument('--defect_region_center', type=str_list, default=None, 
                   help='缺陷区域中心点，格式:[x,y,z]。如果为None则随机选择')
parser.add_argument('--defect_region_radius', type=float, default=0.05, 
                   help='缺陷影响区域的半径(归一化点云坐标系下)')
parser.add_argument('--n_diffusion_steps', type=int, default=25, 
                   help='用于生成缺陷的扩散步数(建议值:25-50，大值会导致计算缓慢)')
parser.add_argument('--noise_scale', type=float, default=0, 
                   help='用于缺陷注入的随机噪声强度')
parser.add_argument('--preserve_global_shape', type=eval, default=True, choices=[True, False], 
                   help='在生成缺陷时是否保持全局形状')
parser.add_argument('--smooth_boundary', type=eval, default=True, choices=[True, False], 
                   help='是否平滑缺陷边界')

# Diffusion解码器特定参数(如适用，从检查点加载)
parser.add_argument('--num_steps', type=int, default=1000, 
                   help='解码器的扩散步数')
parser.add_argument('--sample_steps_decode', type=int, default=None, 
                   help='快速解码的步数(None表示完整步数)')
parser.add_argument('--beta_1', type=float, default=1e-4, 
                   help='初始beta值用于方差调度')
parser.add_argument('--beta_T', type=float, default=0.02, 
                   help='最终beta值用于方差调度')
parser.add_argument('--sched_mode', type=str, default='cosine', 
                   help='方差调度类型')
parser.add_argument('--flexibility', type=float, default=0.0, 
                   help='采样灵活性')

# 其他参数
parser.add_argument('--seed', type=int, default=2024)
parser.add_argument('--device', type=str, default='cuda')
parser.add_argument('--output_dir', type=str, default='./generated_anomalies')
parser.add_argument('--vis_samples', type=int, default=5, help='要可视化的样本数量')
args = parser.parse_args()

# 创建输出目录
os.makedirs(args.output_dir, exist_ok=True)
# 设置日志
log_dir = get_new_log_dir(os.path.join(args.output_dir, 'logs'), prefix='ANOM_')
logger = get_logger('anomaly_generation', log_dir)
logger.info(args)
# 设置随机种子
seed_all(args.seed)

# 加载模型
def load_model():
    logger.info(f"加载预训练模型: {args.checkpoint_path}")
    
    # 加载checkpoint
    try:
        checkpoint = torch.load(args.checkpoint_path, map_location=args.device)
        logger.info("成功加载检查点")
    except Exception as e:
        logger.error(f"无法加载检查点: {e}")
        return None
    
    # 合并参数
    effective_args = args
    if 'args' in checkpoint and isinstance(checkpoint['args'], argparse.Namespace):
        logger.info("检查点包含'args'，正在合并...")
        ckpt_args = checkpoint['args']
        
        # 创建一个命令行显式设置的参数集合
        cmd_line_args = {k: v for k, v in vars(args).items() if v is not parser.get_default(k)}
        
        # 必需的模型参数，确保它们被设置
        required_model_params = ['latent_dim', 'use_vae', 'num_steps', 'beta_1', 'beta_T', 'sched_mode']
        
        for k, v in vars(ckpt_args).items():
            if hasattr(effective_args, k) and k not in cmd_line_args:
                setattr(effective_args, k, v)
                logger.info(f"从检查点加载参数: {k}={v}")
            elif k in required_model_params and not hasattr(effective_args, k):
                # 对于必需的模型参数，即使参数不存在也添加它
                setattr(effective_args, k, v)
                logger.info(f"从检查点添加关键参数: {k}={v}")
    
    # 打印关键参数用于调试
    logger.info("--- 关键模型参数 ---")
    key_params = ['latent_dim', 'use_vae', 'num_steps', 'beta_1', 'beta_T', 'sched_mode', 'flexibility']
    for param in key_params:
        if hasattr(effective_args, param):
            logger.info(f"  {param}: {getattr(effective_args, param)}")
        else:
            logger.warning(f"  {param}: 未设置！")
    
    # 构建模型
    try:
        model = AutoEncoder(effective_args).to(args.device)
    
        # 加载模型权重
        if 'state_dict' in checkpoint: 
            model.load_state_dict(checkpoint['state_dict'])
        elif 'model_state_dict' in checkpoint: 
            model.load_state_dict(checkpoint['model_state_dict'])
        else: 
            model.load_state_dict(checkpoint)
        logger.info('成功加载AutoEncoder模型状态字典')
    except Exception as e:
        logger.error(f"无法创建或加载AutoEncoder: {e}")
        logger.error("请检查模型参数是否完整")
        return None
    
    model.eval()
    logger.info("模型加载完成")
    return model, effective_args

# 生成点云
def generate_point_clouds(model, effective_args):
    """使用解码器生成点云样本，可以是正常样本或者异常样本"""
    logger.info(f"使用解码器生成点云样本，模式: {args.generate_mode}")
    
    if args.generate_mode == 'random_latent':
        # 从随机潜在向量生成
        return generate_from_random_latent(model, effective_args)
    else:
        # 从数据集生成
        return generate_from_dataset(model, effective_args)

def generate_from_random_latent(model, effective_args):
    """从随机潜在向量生成点云样本"""
    generated_normal_pcs = []
    generated_anomaly_pcs = []
    anomaly_types = []
    
    latent_dim = effective_args.latent_dim
    
    # 计算生成所需批次
    num_batches = math.ceil(args.num_samples / args.batch_size)
    
    for i in tqdm(range(num_batches), desc="从随机潜在向量生成点云"):
        batch_size = min(args.batch_size, args.num_samples - i * args.batch_size)
        
        # 创建随机潜在向量
        z = torch.randn(batch_size, latent_dim).to(args.device)
        
        # 使用解码器生成正常点云
        normal_points = model.decode(
            code=z,
            num_points=args.num_points
        )
        
        # 对每个生成的正常点云添加缺陷
        anomaly_points_batch = []
        anomaly_types_batch = []
        
        for j in range(batch_size):
            latent_j = z[j:j+1]  # 提取单个潜在向量
            
            # 选择缺陷类型
            if args.defect_type == 'random':
                defect_type = random.choice(['cut', 'bump', 'dent'])
            elif args.defect_type == 'all':
                # 对每个样本依次使用不同类型的缺陷
                defect_types = ['cut', 'bump', 'dent']
                defect_type = defect_types[(i * batch_size + j) % len(defect_types)]
            else:
                defect_type = args.defect_type
            
            # 随机选择缺陷中心点（如果没有指定）
            if args.defect_region_center is None:
                with torch.no_grad():
                    # 从解码的点云中随机选择一个点作为缺陷中心
                    idx = torch.randint(0, normal_points.size(1), (1,)).item()
                    defect_center = normal_points[j, idx].clone()
            else:
                defect_center = torch.tensor(
                    [float(x) for x in args.defect_region_center], 
                    dtype=torch.float32,
                    device=args.device
                )
            
            # 创建空间掩码
            mask = create_spatial_mask(
                normal_points[j], 
                defect_center, 
                args.defect_region_radius,
                smooth_falloff=args.smooth_boundary
            ).to(args.device)
            
            # 确保掩码在正确的设备上
            mask = mask.to(args.device)
            
            # 应用缺陷
            anomaly_points = apply_latent_defect(
                latent_j,
                defect_type,
                args.defect_severity,
                model.decode,
                args.num_points,
                noise_scale=args.noise_scale,
                n_steps=args.n_diffusion_steps,
                mask=mask.unsqueeze(0) if args.preserve_global_shape else None
            )
            
            anomaly_points_batch.append(anomaly_points)
            anomaly_types_batch.append(defect_type)
        
        # 合并当前批次的结果
        anomaly_points_batch = torch.cat(anomaly_points_batch, dim=0)
        
        # 统一归一化处理
        normal_centers = normal_points.mean(dim=1, keepdim=True)
        normal_points_centered = normal_points - normal_centers
        max_abs = normal_points_centered.abs().max(dim=1, keepdim=True)[0].max(dim=2, keepdim=True)[0]
        scaling_factor = 1.0 / (max_abs + 1e-10)
        normal_points_norm = normal_points_centered * scaling_factor
        
        # 对异常点云应用相同的变换
        anomaly_points_centered = anomaly_points_batch - normal_centers
        anomaly_points_norm = anomaly_points_centered * scaling_factor
        
        # 添加到结果列表
        generated_normal_pcs.append(normal_points_norm.detach().cpu())
        generated_anomaly_pcs.append(anomaly_points_norm.detach().cpu())
        anomaly_types.extend(anomaly_types_batch)
    
    # 合并所有生成的点云
    generated_normal_pcs = torch.cat(generated_normal_pcs, dim=0)[:args.num_samples]
    generated_anomaly_pcs = torch.cat(generated_anomaly_pcs, dim=0)[:args.num_samples]
    anomaly_types = anomaly_types[:args.num_samples]
    
    # 记录归一化后的点云范围信息
    normal_min = generated_normal_pcs.min().item()
    normal_max = generated_normal_pcs.max().item()
    anomaly_min = generated_anomaly_pcs.min().item()
    anomaly_max = generated_anomaly_pcs.max().item()
    
    logger.info(f"生成正常点云范围: [{normal_min:.4f}, {normal_max:.4f}]")
    logger.info(f"生成异常点云范围: [{anomaly_min:.4f}, {anomaly_max:.4f}]")
    
    return generated_normal_pcs, generated_anomaly_pcs, anomaly_types

def generate_from_dataset(model, effective_args):
    """从数据集生成点云样本，基于真实点云添加缺陷"""
    logger.info("从数据集生成异常点云...")
    
    # 创建数据集和数据加载器
    eval_transform = None
    if effective_args.pca_align_eval:
        logger.info("对评估数据应用PCA对齐")
        eval_transform = PCARotate(attr_keys=['pointcloud'], random_flip=False, always_align_y_to_gravity=False)
    
    eval_dset = Real3DADDataset(
        path=effective_args.dataset_path, 
        categories=effective_args.categories,
        split=effective_args.dataset_split, 
        scale_mode=effective_args.scale_mode,
        transform=eval_transform, 
        num_points=effective_args.num_points,
        sampling_method=effective_args.sampling_method
    )
    
    if len(eval_dset) == 0:
        logger.error(f"评估数据集为空，分割'{effective_args.dataset_split}'")
        return None, None, None
    
    eval_loader = DataLoader(
        eval_dset, 
        batch_size=effective_args.batch_size,
        shuffle=True,  # 随机顺序便于多样性
        num_workers=effective_args.num_workers, 
        drop_last=False, 
        pin_memory=True,
        collate_fn=custom_collate_fn
    )
    
    # 用于存储结果
    normal_pcs = []
    anomaly_pcs = []
    anomaly_types = []
    
    # 记录生成的样本数
    num_generated = 0
    
    # 遍历数据集
    for batch_idx, batch_data in enumerate(tqdm(eval_loader, desc="生成异常点云")):
        if batch_data is None:
            continue
        
        if num_generated >= args.num_samples:
            break
        
        # 获取原始点云
        normal_pcs_batch = batch_data['pointcloud'].to(args.device)
        shift_from_batch = batch_data.get('shift')
        scale_from_batch = batch_data.get('scale')
        
        # 确保shift和scale是tensor且在正确的设备上
        default_shift = torch.zeros(normal_pcs_batch.size(0), 1, 3).to(args.device)
        default_scale = torch.ones(normal_pcs_batch.size(0), 1, 1).to(args.device)
        
        shift_batch = torch.as_tensor(
            shift_from_batch if shift_from_batch is not None else default_shift,
            dtype=torch.float32
        ).to(args.device)
        
        scale_batch = torch.as_tensor(
            scale_from_batch if scale_from_batch is not None else default_scale,
            dtype=torch.float32
        ).to(args.device)
        
        # 获取每个样本的类别和ID
        categories = batch_data.get('category', ['unknown'] * normal_pcs_batch.size(0))
        model_ids = batch_data.get('model_id', [f'model_{i}' for i in range(normal_pcs_batch.size(0))])
        
        # 处理每个点云样本
        for i in range(normal_pcs_batch.size(0)):
            if num_generated >= args.num_samples:
                break
                
            # 获取单个点云
            normal_pc = normal_pcs_batch[i:i+1]
            
            # 编码点云到潜在空间
            with torch.no_grad():
                if effective_args.use_vae:
                    mean_val, log_var_val = model.encode(normal_pc, return_log_variance=True)
                    # 对于缺陷生成，我们可以直接使用均值，而不是采样
                    latent = reparameterize_gaussian(mean_val, log_var_val)
                else:
                    latent = model.encode(normal_pc, return_log_variance=False)
                    if isinstance(latent, tuple):
                        latent = latent[0]
            
            # 选择缺陷类型
            if args.defect_type == 'random':
                defect_type = random.choice(['cut', 'bump', 'dent'])
            elif args.defect_type == 'all':
                # 对每个样本依次使用不同类型的缺陷
                defect_types = ['cut', 'bump', 'dent']
                defect_type = defect_types[num_generated % len(defect_types)]
            else:
                defect_type = args.defect_type
            
            # 选择或计算缺陷区域中心
            if args.defect_region_center is None:
                # 随机选择点云中的一个点作为缺陷中心
                idx = torch.randint(0, normal_pc.size(1), (1,)).item()
                defect_center = normal_pc[0, idx].clone()
            else:
                # 使用指定的缺陷中心
                defect_center = torch.tensor(
                    [float(x) for x in args.defect_region_center], 
                    dtype=torch.float32,
                    device=args.device
                )
            
            # 创建空间掩码
            mask = create_spatial_mask(
                normal_pc[0], 
                defect_center, 
                args.defect_region_radius,
                smooth_falloff=args.smooth_boundary
            ).to(args.device)
            
            # 确保掩码在正确的设备上
            mask = mask.to(args.device)
            
            # 应用缺陷
            anomaly_pc = apply_latent_defect(
                latent,
                defect_type,
                args.defect_severity,
                model.decode,
                normal_pc.size(1),
                noise_scale=args.noise_scale,
                n_steps=args.n_diffusion_steps,
                mask=mask.unsqueeze(0) if args.preserve_global_shape else None
            )
            
            # 添加到结果列表
            normal_pcs.append(normal_pc.cpu())
            anomaly_pcs.append(anomaly_pc.cpu())
            anomaly_types.append(defect_type)
            
            num_generated += 1
    
    # 合并所有生成的点云
    normal_pcs = torch.cat(normal_pcs, dim=0)[:args.num_samples]
    anomaly_pcs = torch.cat(anomaly_pcs, dim=0)[:args.num_samples]
    anomaly_types = anomaly_types[:args.num_samples]
    
    logger.info(f"成功生成 {len(normal_pcs)} 对正常/异常点云样本")
    
    return normal_pcs, anomaly_pcs, anomaly_types

# 可视化点云
def visualize_point_clouds(normal_pcs, anomaly_pcs, anomaly_types):
    logger.info("可视化点云样本...")
    num_vis = min(args.vis_samples, len(normal_pcs))
    
    # 使用TensorBoard进行3D点云可视化
    writer = SummaryWriter(log_dir=os.path.join(log_dir, 'vis'))
    
    for i in range(num_vis):
        # 正常点云 - 使用蓝色
        normal_color = torch.ones_like(normal_pcs[i:i+1]) * torch.tensor([0.0, 0.0, 1.0])
        writer.add_mesh(f'normal_{i}', normal_pcs[i:i+1], colors=normal_color)
        
        # 异常点云 - 根据缺陷类型使用不同颜色
        anomaly_color = torch.ones_like(anomaly_pcs[i:i+1])
        if anomaly_types[i] == 'cut':
            anomaly_color = anomaly_color * torch.tensor([1.0, 0.0, 0.0])  # 红色
        elif anomaly_types[i] == 'bump':
            anomaly_color = anomaly_color * torch.tensor([0.0, 1.0, 0.0])  # 绿色
        elif anomaly_types[i] == 'dent':
            anomaly_color = anomaly_color * torch.tensor([1.0, 0.5, 0.0])  # 橙色
        
        writer.add_mesh(f'anomaly_{anomaly_types[i]}_{i}', anomaly_pcs[i:i+1], colors=anomaly_color)
    
    # 绘制点分布直方图
    fig, axs = plt.subplots(3, 2, figsize=(12, 12))
    
    for i in range(3):  # x, y, z轴
        # 正常点云
        axs[i, 0].hist(normal_pcs[:,:,i].flatten().numpy(), bins=50, alpha=0.7, color='blue')
        axs[i, 0].set_title(f'正常点云分布 - 轴 {i}')
        axs[i, 0].set_xlim([-1.1, 1.1])
        
        # 异常点云
        axs[i, 1].hist(anomaly_pcs[:,:,i].flatten().numpy(), bins=50, alpha=0.7, color='red')
        axs[i, 1].set_title(f'异常点云分布 - 轴 {i}')
        axs[i, 1].set_xlim([-1.1, 1.1])
    
    plt.tight_layout()
    plt.savefig(os.path.join(log_dir, 'distribution_comparison.png'))
    
    # 额外添加一个散点图来可视化点分布
    if num_vis > 0:
        for idx in range(min(3, num_vis)):
            fig = plt.figure(figsize=(15, 10))
            
            # 选择一个样本展示正常和异常点云
            sample_idx = idx
            defect_type = anomaly_types[sample_idx]
            
            # 3D视图
            ax1 = fig.add_subplot(2, 3, 1, projection='3d')
            ax1.scatter(
                normal_pcs[sample_idx, :, 0].numpy(),
                normal_pcs[sample_idx, :, 1].numpy(),
                normal_pcs[sample_idx, :, 2].numpy(),
                s=1, alpha=0.5, c='blue'
            )
            ax1.set_title('正常点云 3D视图')
            ax1.set_xlim([-1.1, 1.1]); ax1.set_ylim([-1.1, 1.1]); ax1.set_zlim([-1.1, 1.1])
            
            ax2 = fig.add_subplot(2, 3, 4, projection='3d')
            ax2.scatter(
                anomaly_pcs[sample_idx, :, 0].numpy(),
                anomaly_pcs[sample_idx, :, 1].numpy(),
                anomaly_pcs[sample_idx, :, 2].numpy(),
                s=1, alpha=0.5, c='red'
            )
            ax2.set_title(f'异常点云 3D视图 ({defect_type})')
            ax2.set_xlim([-1.1, 1.1]); ax2.set_ylim([-1.1, 1.1]); ax2.set_zlim([-1.1, 1.1])
            
            # XY平面
            ax3 = fig.add_subplot(2, 3, 2, projection='3d')
            ax3.scatter(
                normal_pcs[sample_idx, :, 0].numpy(),
                normal_pcs[sample_idx, :, 1].numpy(),
                np.zeros_like(normal_pcs[sample_idx, :, 2].numpy()),
                s=1, alpha=0.5, c='blue'
            )
            ax3.set_title('正常点云 XY平面')
            ax3.set_xlim([-1.1, 1.1]); ax3.set_ylim([-1.1, 1.1]); ax3.set_zlim([-1.1, 1.1])
            
            ax4 = fig.add_subplot(2, 3, 5, projection='3d')
            ax4.scatter(
                anomaly_pcs[sample_idx, :, 0].numpy(),
                anomaly_pcs[sample_idx, :, 1].numpy(),
                np.zeros_like(anomaly_pcs[sample_idx, :, 2].numpy()),
                s=1, alpha=0.5, c='red'
            )
            ax4.set_title(f'异常点云 XY平面 ({defect_type})')
            ax4.set_xlim([-1.1, 1.1]); ax4.set_ylim([-1.1, 1.1]); ax4.set_zlim([-1.1, 1.1])
            
            # XZ平面
            ax5 = fig.add_subplot(2, 3, 3, projection='3d')
            ax5.scatter(
                normal_pcs[sample_idx, :, 0].numpy(),
                np.zeros_like(normal_pcs[sample_idx, :, 1].numpy()),
                normal_pcs[sample_idx, :, 2].numpy(),
                s=1, alpha=0.5, c='blue'
            )
            ax5.set_title('正常点云 XZ平面')
            ax5.set_xlim([-1.1, 1.1]); ax5.set_ylim([-1.1, 1.1]); ax5.set_zlim([-1.1, 1.1])
            
            ax6 = fig.add_subplot(2, 3, 6, projection='3d')
            ax6.scatter(
                anomaly_pcs[sample_idx, :, 0].numpy(),
                np.zeros_like(anomaly_pcs[sample_idx, :, 1].numpy()),
                anomaly_pcs[sample_idx, :, 2].numpy(),
                s=1, alpha=0.5, c='red'
            )
            ax6.set_title(f'异常点云 XZ平面 ({defect_type})')
            ax6.set_xlim([-1.1, 1.1]); ax6.set_ylim([-1.1, 1.1]); ax6.set_zlim([-1.1, 1.1])
            
            plt.tight_layout()
            plt.savefig(os.path.join(log_dir, f'pointcloud_comparison_{idx}.png'))
    
    writer.close()

# 保存生成的点云
def save_point_clouds(normal_pcs, anomaly_pcs, anomaly_types):
    logger.info(f"保存生成的点云到 {args.output_dir} 和 {log_dir}...")
    normal_np = normal_pcs.numpy()
    anomaly_np = anomaly_pcs.numpy()
    
    # 创建pcd保存目录
    pcd_dir = os.path.join(log_dir, 'pcd_files')
    os.makedirs(pcd_dir, exist_ok=True)
    
    # 分别创建正常和异常点云保存目录
    normal_dir = os.path.join(pcd_dir, 'normal')
    anomaly_dir = os.path.join(pcd_dir, 'anomaly')
    os.makedirs(normal_dir, exist_ok=True)
    os.makedirs(anomaly_dir, exist_ok=True)
    
    # 创建各类型异常的子目录
    defect_dirs = {}
    for defect_type in set(anomaly_types):
        defect_dirs[defect_type] = os.path.join(anomaly_dir, defect_type)
        os.makedirs(defect_dirs[defect_type], exist_ok=True)
    
    # 保存单个点云文件（同时保存.npy和.pcd格式）
    for i in range(len(normal_pcs)):
        # 获取缺陷类型
        defect_type = anomaly_types[i]
        
        # 保存正常点云
        normal_pcd = o3d.geometry.PointCloud()
        normal_pcd.points = o3d.utility.Vector3dVector(normal_np[i])
        normal_colors = np.ones_like(normal_np[i]) * np.array([0.0, 0.0, 1.0])  # 蓝色
        normal_pcd.colors = o3d.utility.Vector3dVector(normal_colors)
        
        normal_pcd_path = os.path.join(normal_dir, f'normal_{i:04d}.pcd')
        normal_npy_path = os.path.join(normal_dir, f'normal_{i:04d}.npy')
        o3d.io.write_point_cloud(normal_pcd_path, normal_pcd)
        np.save(normal_npy_path, normal_np[i])
        
        # 保存异常点云
        anomaly_pcd = o3d.geometry.PointCloud()
        anomaly_pcd.points = o3d.utility.Vector3dVector(anomaly_np[i])
        
        # 根据缺陷类型设置颜色
        if defect_type == 'cut':
            anomaly_colors = np.ones_like(anomaly_np[i]) * np.array([1.0, 0.0, 0.0])  # 红色
        elif defect_type == 'bump':
            anomaly_colors = np.ones_like(anomaly_np[i]) * np.array([0.0, 1.0, 0.0])  # 绿色
        elif defect_type == 'dent':
            anomaly_colors = np.ones_like(anomaly_np[i]) * np.array([1.0, 0.5, 0.0])  # 橙色
        else:
            anomaly_colors = np.ones_like(anomaly_np[i]) * np.array([0.5, 0.5, 0.5])  # 灰色
        
        anomaly_pcd.colors = o3d.utility.Vector3dVector(anomaly_colors)
        
        # 保存到对应缺陷类型的目录
        anomaly_pcd_path = os.path.join(defect_dirs[defect_type], f'anomaly_{defect_type}_{i:04d}.pcd')
        anomaly_npy_path = os.path.join(defect_dirs[defect_type], f'anomaly_{defect_type}_{i:04d}.npy')
        o3d.io.write_point_cloud(anomaly_pcd_path, anomaly_pcd)
        np.save(anomaly_npy_path, anomaly_np[i])
        
        # 同时保存一个配对的点云文件（用于后续评估）
        pair_dir = os.path.join(pcd_dir, 'pairs')
        os.makedirs(pair_dir, exist_ok=True)
        
        pair_data = {
            'normal': normal_np[i],
            'anomaly': anomaly_np[i],
            'defect_type': defect_type,
            'defect_severity': args.defect_severity,
            'defect_radius': args.defect_region_radius
        }
        
        pair_path = os.path.join(pair_dir, f'pair_{i:04d}.npz')
        np.savez(pair_path, **pair_data)
    
    logger.info(f"已保存 {len(normal_pcs)} 对正常/异常点云样本")
    logger.info(f"点云文件保存在: {pcd_dir}")
    
    # 保存异常类型列表
    with open(os.path.join(log_dir, 'anomaly_types.txt'), 'w') as f:
        for i, defect_type in enumerate(anomaly_types):
            f.write(f"{i:04d}: {defect_type}\n")

# 创建空间掩码函数
def create_spatial_mask(points, center, radius, smooth_falloff=True, falloff_factor=2.0):
    """
    创建一个空间掩码，基于点到中心的距离
    
    Args:
        points (torch.Tensor): 形状为[N, 3]的点云
        center (torch.Tensor): 形状为[3]的中心点
        radius (float): 掩码的半径
        smooth_falloff (bool): 是否使用平滑的边界过渡
        falloff_factor (float): 控制边界平滑过渡的陡峭程度
        
    Returns:
        torch.Tensor: 形状为[N]的掩码，值在0到1之间
    """
    # 计算每个点到中心的距离
    distances = torch.norm(points - center, dim=1)
    
    if smooth_falloff:
        # 使用sigmoid函数创建平滑过渡
        # 距离小于radius的点掩码值接近1，大于radius的点掩码值接近0
        mask = torch.sigmoid(falloff_factor * (radius - distances))
    else:
        # 硬阈值掩码
        mask = (distances <= radius).float()
    
    return mask

# 应用潜在空间缺陷函数
def apply_latent_defect(latent, defect_type, severity, model_decoder, num_points,
                        noise_scale=0.1, n_steps=25, mask=None):
    """
    在潜在空间中应用缺陷并通过扩散过程优化
    
    Args:
        latent (torch.Tensor): 原始潜在向量，形状为[B, latent_dim]
        defect_type (str): 缺陷类型: 'cut', 'bump', 或 'dent'
        severity (float): 缺陷严重程度(扰动强度)
        model_decoder: 模型解码器
        num_points (int): 输出点云中的点数
        noise_scale (float): 添加的噪声比例
        n_steps (int): 扩散优化步数
        mask (torch.Tensor, optional): 空间掩码，用于引导缺陷位置
        
    Returns:
        torch.Tensor: 应用缺陷后的点云，形状为[B, num_points, 3]
    """
    device = latent.device
    batch_size = latent.shape[0]
    latent_dim = latent.shape[1]
    
    # 确保掩码在正确的设备上
    if mask is not None:
        mask = mask.to(device)
    
    # 复制原始潜在向量
    defect_latent = latent.clone()
    
    # 根据缺陷类型调整潜在向量
    if defect_type == 'cut':
        # 对于切口，我们在潜在空间中添加结构化噪声
        # 首先找到潜在空间的主成分方向
        with torch.no_grad():
            # 生成多个正常样本的潜在向量来找主成分
            temp_noise = torch.randn(10, latent_dim, device=device) * 0.1
            temp_latents = latent.repeat(10, 1) + temp_noise
            temp_points = model_decoder(temp_latents, num_points).detach()
            temp_points_np = temp_points.reshape(-1, 3).cpu().numpy()
            
            # 使用PCA找主要变化方向
            pca = PCA(n_components=3)
            pca.fit(temp_points_np)
            principal_dir = torch.from_numpy(pca.components_[0]).float().to(device)
            
        # 在主成分方向上添加有针对性的扰动
        cut_noise = torch.randn(batch_size, latent_dim, device=device) * noise_scale
        cut_noise = cut_noise - torch.outer(
            torch.ones(batch_size, device=device),
            torch.mean(cut_noise, dim=0)
        ) * 0.5  # 使噪声更加定向
        defect_latent = defect_latent + severity * cut_noise
        
    elif defect_type == 'bump':
        # 对于凸起，我们向增加体积的方向移动
        bump_dir = torch.randn(latent_dim, device=device)
        bump_dir = bump_dir / torch.norm(bump_dir)
        defect_latent = defect_latent + severity * bump_dir * noise_scale
        
    elif defect_type == 'dent':
        # 对于凹陷，我们向减少体积的方向移动
        dent_dir = torch.randn(latent_dim, device=device)
        dent_dir = dent_dir / torch.norm(dent_dir)
        defect_latent = defect_latent - severity * dent_dir * noise_scale
    
    # 如果扩散步数很大，使用简化的方法
    if n_steps > 10000:
        logger.info(f"检测到大量扩散步数({n_steps})，使用简化计算...")
        # 使用直接解码方法
        steps_to_use = min(25, n_steps // 40)  # 最多使用25步或总步数的1/40
        logger.info(f"使用{steps_to_use}步进行扩散优化...")
        
        # 使用简化的去噪过程，减少迭代次数
        for t in range(steps_to_use):
            # 添加少量噪声
            noise_factor = noise_scale / (t + 1) * (0.1 if t > steps_to_use // 2 else 0.5)
            noise_t = torch.randn_like(defect_latent) * noise_factor
            defect_latent = defect_latent + noise_t
            
            # 每隔几步解码一次，避免过多计算
            if t % max(1, steps_to_use // 5) == 0 or t == steps_to_use - 1:
                with torch.no_grad():
                    points_t = model_decoder(defect_latent, num_points)
                    
                    # 如果提供了掩码，将未受影响区域的点替换回原始点
                    if mask is not None:
                        # 解码原始潜在向量
                        orig_points = model_decoder(latent, num_points)
                        
                        # 扩展掩码以适应点云形状
                        mask_expanded = mask.unsqueeze(-1).expand_as(points_t)
                        
                        # 混合原始点和缺陷点
                        points_t = mask_expanded * points_t + (1 - mask_expanded) * orig_points
    else:
        # 使用原始扩散过程优化缺陷潜在向量
        for t in range(n_steps):
            # 添加少量噪声并去噪
            noise_t = torch.randn_like(defect_latent) * (noise_scale / (t + 1))
            defect_latent = defect_latent + noise_t
            
            # 解码并重编码，以使潜在向量符合分布
            with torch.no_grad():
                points_t = model_decoder(defect_latent, num_points)
                
                # 如果提供了掩码，将未受影响区域的点替换回原始点
                if mask is not None:
                    # 解码原始潜在向量
                    orig_points = model_decoder(latent, num_points)
                    
                    # 确保点云和掩码在同一设备上
                    points_t = points_t.to(device)
                    orig_points = orig_points.to(device)
                    
                    # 扩展掩码以适应点云形状
                    mask_expanded = mask.unsqueeze(-1).expand_as(points_t)
                    
                    # 混合原始点和缺陷点
                    points_t = mask_expanded * points_t + (1 - mask_expanded) * orig_points
            
            # 保持一定随机性以获得自然效果
            if t < n_steps - 1:  # 最后一步不添加随机性
                defect_latent = defect_latent + torch.randn_like(defect_latent) * (noise_scale / (n_steps - t))
    
    # 解码最终潜在向量
    with torch.no_grad():
        defect_points = model_decoder(defect_latent, num_points)
        
        # 最后一次应用掩码，确保非缺陷区域保持原样
        if mask is not None:
            orig_points = model_decoder(latent, num_points)
            
            # 确保点云和掩码在同一设备上
            defect_points = defect_points.to(device)
            orig_points = orig_points.to(device)
            
            mask_expanded = mask.unsqueeze(-1).expand_as(defect_points)
            defect_points = mask_expanded * defect_points + (1 - mask_expanded) * orig_points
    
    return defect_points

# 主函数
def main():
    # 1. 加载预训练模型
    model_result = load_model()
    if model_result is None:
        logger.error("模型加载失败，终止执行。")
        return
    
    model, effective_args = model_result
    
    # 确保必要的参数都已设置
    required_params = {
        'num_steps': 1000,           # 扩散步数
        'beta_1': 1e-4,              # 初始beta
        'beta_T': 0.02,              # 最终beta
        'sched_mode': 'cosine',      # 方差调度类型
        'flexibility': 0.0,          # 采样灵活性
        'latent_dim': 256,           # 潜在空间维度
        'use_vae': True,             # 是否使用VAE
        'n_diffusion_steps': 25,     # 缺陷生成扩散步数
        'noise_scale': 0.1,          # 噪声强度
        'defect_severity': 1.0,      # 缺陷严重程度
        'defect_region_radius': 0.2, # 缺陷区域半径
    }
    
    params_added = []
    for param, default_value in required_params.items():
        if not hasattr(effective_args, param):
            setattr(effective_args, param, default_value)
            params_added.append(f"{param}={default_value}")
    
    if params_added:
        logger.warning(f"为以下缺失参数添加默认值: {', '.join(params_added)}")
    
    # 打印生成设置
    logger.info("=== 缺陷生成设置 ===")
    logger.info(f"- 缺陷类型: {effective_args.defect_type}")
    logger.info(f"- 严重程度: {effective_args.defect_severity}")
    logger.info(f"- 缺陷区域半径: {effective_args.defect_region_radius}")
    logger.info(f"- 噪声强度: {effective_args.noise_scale}")
    logger.info(f"- 扩散步数: {effective_args.n_diffusion_steps}")
    logger.info(f"- 全局形状保持: {effective_args.preserve_global_shape}")
    logger.info(f"- 平滑边界: {effective_args.smooth_boundary}")
    
    # 2. 生成点云样本（同时生成正常和异常点云）
    logger.info("开始生成点云...")
    generation_result = generate_point_clouds(model, effective_args)
    
    if generation_result is None:
        logger.error("点云生成失败，终止执行。")
        return
    
    normal_pcs, anomaly_pcs, anomaly_types = generation_result
    
    logger.info(f"成功生成 {len(normal_pcs)} 对正常/异常点云样本")
    logger.info(f"缺陷类型统计: " + 
               ", ".join([f"{defect}: {anomaly_types.count(defect)}" 
                         for defect in sorted(set(anomaly_types))]))
    
    # 3. 可视化点云
    logger.info("开始可视化点云...")
    visualize_point_clouds(normal_pcs, anomaly_pcs, anomaly_types)
    
    # 4. 保存生成的点云
    logger.info("保存点云结果...")
    save_point_clouds(normal_pcs, anomaly_pcs, anomaly_types)
    
    logger.info("异常点云生成完成！")
    logger.info(f"结果保存在: {log_dir}")

if __name__ == '__main__':
    main()
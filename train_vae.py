#!/usr/bin/env python3

import os, math, argparse, random, time
import numpy as np
import torch, torch.nn as nn
from torch.utils.data import DataLoader
from tqdm.auto import tqdm
from evaluation.evaluation_metrics import distChamfer
from utils.dataset import Real3DADDataset
from utils.data import custom_collate_fn
from utils.transform import PCARotate
from utils.misc import seed_all, get_logger, get_new_log_dir, log_hyperparams, BlackHole, CheckpointManager
from models.encoders.pointnet import PointTransformerV3Encoder
from emd import earth_mover_distance
import torch.nn.functional as F
from utils.transform import *
from torchvision import transforms
from torch.optim.lr_scheduler import SequentialLR, LinearLR, CosineAnnealingLR


# SummaryWriter may reside in torch.utils.tensorboard or separate package
try:
    from torch.utils.tensorboard import SummaryWriter
except ImportError:
    SummaryWriter = None

# ---------------------- Argument parser ---------------------- #
parser = argparse.ArgumentParser()
# 数据 & 训练
parser.add_argument('--dataset_path', type=str, default='/home/<USER>/llm/3DAD/data/Real3D-AD-PCD')
parser.add_argument('--categories', nargs='+', default=['airplane'])
parser.add_argument('--scale_mode', type=str, default='shape_bbox')
parser.add_argument('--num_points', type=int, default=2048)
parser.add_argument('--train_batch_size', type=int, default=4)
parser.add_argument('--val_batch_size', type=int, default=4)
parser.add_argument('--num_workers', type=int, default=1)
# 模型
parser.add_argument('--latent_dim', type=int, default=256)
# 损失系数
parser.add_argument('--kl_weight', type=float, default=0.0001, help='最终 KL 系数')
parser.add_argument('--kl_warmup_iters', type=int, default=5000)
# 优化器
parser.add_argument('--lr', type=float, default=1e-4)
parser.add_argument('--weight_decay', type=float, default=0.)
parser.add_argument('--max_grad_norm', type=float, default=1)
parser.add_argument('--warmup_iters',type=int,default=2000)
# 训练控制
parser.add_argument('--max_iters', type=int, default=20000)
parser.add_argument('--val_freq', type=int, default=1000)
parser.add_argument('--seed', type=int, default=42)
parser.add_argument('--device', type=str, default='cuda')
parser.add_argument('--resume', type=str, default=None)
# 日志
parser.add_argument('--logging', type=eval, default=True, choices=[True, False])
parser.add_argument('--log_root', type=str, default='./logs_vae')
parser.add_argument('--tag', type=str, default=None)
args = parser.parse_args()
seed_all(args.seed)

# ---------------------- Logger & TB ---------------------- #
log_dir = get_new_log_dir(args.log_root, prefix='VAE_', postfix='_' + args.tag if args.tag else '') if args.logging else None
logger = get_logger('train_vae', log_dir)
if args.logging and SummaryWriter is not None:
    writer = SummaryWriter(log_dir)
    log_hyperparams(writer, args)
else:
    writer = BlackHole()
ckpt_mgr = CheckpointManager(log_dir) if args.logging else BlackHole()
logger.info(args)
ATTR_KEYS_TO_TRANSFORM = ['pointcloud'] 
# ---------------------- Dataset ---------------------- #
pca_align = PCARotate(
    attr_keys=ATTR_KEYS_TO_TRANSFORM, 
    random_flip=False,      
    always_align_y_to_gravity=True
)
train_transform = transforms.Compose([
    pca_align
])


train_dset = Real3DADDataset(
    path=args.dataset_path, categories=args.categories, split='train',
    scale_mode=args.scale_mode, transform=train_transform, num_points=args.num_points, sampling_method='hybrid'
)

val_dset = Real3DADDataset(
    path=args.dataset_path, categories=args.categories, split='train',
    scale_mode=args.scale_mode, transform=train_transform, num_points=args.num_points, sampling_method='hybrid'
)
logger.info(f"Train set: {len(train_dset)} | Val set: {len(val_dset)}")
train_loader = DataLoader(train_dset, batch_size=args.train_batch_size, shuffle=True,
                          num_workers=args.num_workers, collate_fn=custom_collate_fn)
val_loader = DataLoader(val_dset, batch_size=args.val_batch_size, shuffle=False,
                        num_workers=args.num_workers, collate_fn=custom_collate_fn)

def get_data_iter(loader):
    while True:
        for batch in loader:
            yield batch
train_iter = get_data_iter(train_loader)

# =======================================================================================
# ===================== INICIO DE LA SECCIÓN DEL DECODIFICADOR MODIFICADO =====================
# =======================================================================================

class TransformerGenerator(nn.Module):
    def __init__(self, latent_dim, num_coarse_points=256, d_model=256, nhead=4, num_decoder_layers=4):
        super().__init__()
        self.num_coarse_points = num_coarse_points
        self.d_model = d_model

        # 1. "Apoderados de Puntos" (Point Proxies) - Consultas aprendibles
        self.point_proxies = nn.Parameter(torch.randn(1, self.num_coarse_points, self.d_model))

        # 2. Proyección del vector latente para que coincida con d_model
        self.latent_proj = nn.Linear(latent_dim, self.d_model)

        # 3. Decodificador de Transformer
        decoder_layer = nn.TransformerDecoderLayer(d_model=self.d_model, nhead=nhead, batch_first=True)
        self.transformer_decoder = nn.TransformerDecoder(decoder_layer, num_layers=num_decoder_layers)

        # 4. Cabezal de predicción para las coordenadas 3D
        self.coord_head = nn.Linear(self.d_model, 3)

    def forward(self, z):
        B = z.size(0)
        memory = self.latent_proj(z).unsqueeze(1)
        tgt = self.point_proxies.expand(B, -1, -1)
        transformer_out = self.transformer_decoder(tgt, memory)
        coarse_points = self.coord_head(transformer_out)
        return coarse_points

class FoldingRefiner(nn.Module):
    def __init__(self, latent_dim, num_points_per_patch=8, d_model=256):
        super().__init__()
        self.num_points_per_patch = num_points_per_patch

        # ======================= INICIO DE LA MODIFICACIÓN =======================
        # 1. Crear una rejilla 2D aleatoria y fija.
        #    Esto elimina la restricción del cuadrado perfecto.
        #    Generamos un conjunto fijo de puntos 2D aleatorios que se reutilizará.
        self.grid = torch.rand(1, self.num_points_per_patch, 2) * 2 - 1 # Puntos aleatorios en [-1, 1]
        # ======================== FIN DE LA MODIFICACIÓN =========================

        # 2. MLP para el plegado (folding)
        self.folding_mlp = nn.Sequential(
            nn.Linear(latent_dim + 3 + 2, d_model),
            nn.ReLU(),
            nn.Linear(d_model, d_model),
            nn.ReLU(),
            nn.Linear(d_model, 3)
        )

    def forward(self, z, coarse_points):
        B, N_coarse, _ = coarse_points.shape
        
        # Mover la rejilla al dispositivo correcto y expandirla
        self.grid = self.grid.to(z.device)
        # La rejilla ya tiene el tamaño de lote 1, solo la expandimos para que coincida con B
        grid_expanded = self.grid.expand(B, -1, -1) 

        z_expanded = z.unsqueeze(1).expand(-1, N_coarse, -1)
        coarse_expanded = coarse_points.unsqueeze(2).expand(-1, -1, self.num_points_per_patch, -1)
        grid_expanded = grid_expanded.unsqueeze(1).expand(-1, N_coarse, -1, -1)

        mlp_input = torch.cat([
            z_expanded.unsqueeze(2).expand(-1, -1, self.num_points_per_patch, -1),
            coarse_expanded,
            grid_expanded
        ], dim=-1)

        offsets = self.folding_mlp(mlp_input)
        dense_points = coarse_expanded + offsets
        
        return dense_points.view(B, -1, 3)

class PoinTrStyleDecoder(nn.Module):
    def __init__(self, latent_dim, num_points):
        super().__init__()
        self.num_coarse_points = 1024
        self.d_model = 256
        self.nhead = 4
        self.num_decoder_layers = 4
        
        if num_points % self.num_coarse_points != 0:
            raise ValueError("num_points debe ser divisible por num_coarse_points")
        self.num_points_per_patch = num_points // self.num_coarse_points

        self.generator = TransformerGenerator(
            latent_dim=latent_dim, 
            num_coarse_points=self.num_coarse_points, 
            d_model=self.d_model,
            nhead=self.nhead,
            num_decoder_layers=self.num_decoder_layers
        )
        self.refiner = FoldingRefiner(
            latent_dim=latent_dim,
            num_points_per_patch=self.num_points_per_patch,
            d_model=self.d_model
        )

    def forward(self, z):
        coarse_points = self.generator(z)
        dense_points = self.refiner(z, coarse_points)
        return dense_points

class PointNetVAE(nn.Module):
    def __init__(self, latent_dim, num_points):
        super().__init__()
        self.encoder = PointTransformerV3Encoder(latent_dim, input_dim=3)
        # Se utiliza el nuevo decodificador estilo PoinTr
        self.decoder = PoinTrStyleDecoder(latent_dim, num_points)
        
    def reparameterize(self, mu, logvar):
        std = torch.exp(0.5 * logvar)
        eps = torch.randn_like(std)
        return mu + eps * std
        
    def forward(self, x):
        mu, logvar = self.encoder(x)
        z = self.reparameterize(mu, logvar)
        recon = self.decoder(z)
        return recon, mu, logvar

# =======================================================================================
# ======================= FIN DE LA SECCIÓN DEL DECODIFICADOR MODIFICADO ======================
# =======================================================================================


def kl_divergence(mu, logvar):
    return -0.5 * torch.mean(torch.sum(1 + logvar - mu.pow(2) - logvar.exp(), dim=1))


model = PointNetVAE(args.latent_dim, args.num_points).to(args.device)
logger.info(model)

if args.resume and args.resume.lower() != 'none':
    state = torch.load(args.resume, map_location=args.device)
    model.load_state_dict(state['state_dict'])
    logger.info(f"Loaded checkpoint from {args.resume}")

optimizer = torch.optim.AdamW(model.parameters(),
                              lr=args.lr,
                              weight_decay=args.weight_decay)

warmup_sched = LinearLR(
    optimizer,
    start_factor=1e-3,
    end_factor=1.0,
    total_iters=args.warmup_iters)

cosine_sched = CosineAnnealingLR(
    optimizer,
    T_max=args.max_iters - args.warmup_iters,
    eta_min=1e-6)

scheduler = SequentialLR(
    optimizer,
    schedulers=[warmup_sched, cosine_sched],
    milestones=[args.warmup_iters])

# ---------------------- Training ---------------------- #
step = 0
best_val = float('inf')
model.train()
while step < args.max_iters:
    batch = next(train_iter)
    points = batch['pointcloud'].to(args.device)
    optimizer.zero_grad()

    recon, mu, logvar = model(points)
    dl, dr = distChamfer(recon, points)
    recon_loss = (dl.mean(1) + dr.mean(1)).mean()

    kl_weight = min(1.0, step / args.kl_warmup_iters) * args.kl_weight
    kl_loss = kl_divergence(mu, logvar)
    loss = recon_loss + kl_weight * kl_loss
    loss.backward()
    torch.nn.utils.clip_grad_norm_(model.parameters(), args.max_grad_norm)
    optimizer.step()
    scheduler.step()

    if step % 50 == 0:
        logger.info(f"Iter {step:06d} | Loss {loss.item():.4f} | Recon {recon_loss.item():.3f} | KL {kl_loss.item():.3f} (w={kl_weight:.3f})")
        writer.add_scalar('train/loss_total', loss.item(), step)
        writer.add_scalar('train/loss_recon', recon_loss.item(), step)
        writer.add_scalar('train/kl', kl_loss.item(), step)
        writer.add_scalar('train/kl_weight', kl_weight, step)
        writer.add_scalar('train/lr', optimizer.param_groups[0]['lr'], step)

    if step > 0 and step % args.val_freq == 0:
        model.eval()
        with torch.no_grad():
            val_recon_cd_list = []
            val_emd_list = []
            val_kl_list = []
            
            pbar = tqdm(val_loader, desc=f"Validation at iter {step}", leave=False)
            for batch_v in pbar:
                p = batch_v['pointcloud'].to(args.device)
                if p is None: continue
                
                recon_v, mu_v, logvar_v = model(p)
                
                dl_v, dr_v = distChamfer(recon_v, p)
                batch_cd = (dl_v.mean(1) + dr_v.mean(1)).sum().item() / p.size(0)
                val_recon_cd_list.append(batch_cd)
                
                try:
                    recon_v_cont = recon_v.contiguous()
                    p_cont = p.contiguous()
                    emd_val = earth_mover_distance(recon_v_cont, p_cont, transpose=False)
                    val_emd_list.append(emd_val.mean().item())
                except Exception as e:
                    logger.warning(f"Could not compute EMD: {e}")

                val_kl_list.append(kl_divergence(mu_v, logvar_v).item())
                
                pbar.set_postfix({
                    'CD': f'{np.mean(val_recon_cd_list):.4f}',
                    'EMD': f'{np.mean(val_emd_list):.4f}' if val_emd_list else 'N/A'
                })

            val_recon_cd = np.mean(val_recon_cd_list) if val_recon_cd_list else 0.0
            val_emd = np.mean(val_emd_list) if val_emd_list else 0.0
            val_kl = np.mean(val_kl_list) if val_kl_list else 0.0
            
            val_loss = val_recon_cd + args.kl_weight * val_kl

            logger.info(f"[Val] Iter {step:06d} | Loss {val_loss:.4f} | Recon_CD {val_recon_cd:.4f} | EMD {val_emd:.4f} | KL {val_kl:.3f}")
            writer.add_scalar('val/loss_total', val_loss, step)
            writer.add_scalar('val/recon_cd', val_recon_cd, step)
            writer.add_scalar('val/emd', val_emd, step)
            writer.add_scalar('val/kl', val_kl, step)
            
            # --- 保存最佳模型 ---
            if val_loss < best_val:
                best_val = val_loss
                logger.info(f"New best validation loss: {best_val:.4f}, saving checkpoint...")
                ckpt_mgr.save(model, args, best_val, step=step)
                
            model.train() # 将模型切回训练模式

    step += 1

# ---------------------- Save final ---------------------- #
if args.logging:
    ckpt_mgr.save(model, args, best_val, step=step)
logger.info('Training finished.')
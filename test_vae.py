import argparse
import os
import torch
import numpy as np
import open3d as o3d
from tqdm import tqdm
import math # Añadido para el decodificador

# --- Import project modules ---
from models.encoders.pointnet import PointTransformerV3Encoder
from utils.dataset import Real3DADDataset
from utils.data import custom_collate_fn
import torch.nn.functional as F
import torch, torch.nn as nn
from utils.transform import *
from torchvision import transforms

# =======================================================================================
# ===================== INICIO DE LA SECCIÓN DEL DECODIFICADOR MODIFICADO =====================
# ========= (Debe ser idéntica a la del script de entrenamiento corregido) =====
# =======================================================================================

class TransformerGenerator(nn.Module):
    def __init__(self, latent_dim, num_coarse_points=256, d_model=256, nhead=4, num_decoder_layers=4):
        super().__init__()
        self.num_coarse_points = num_coarse_points
        self.d_model = d_model
        self.point_proxies = nn.Parameter(torch.randn(1, self.num_coarse_points, self.d_model))
        self.latent_proj = nn.Linear(latent_dim, self.d_model)
        decoder_layer = nn.TransformerDecoderLayer(d_model=self.d_model, nhead=nhead, batch_first=True)
        self.transformer_decoder = nn.TransformerDecoder(decoder_layer, num_layers=num_decoder_layers)
        self.coord_head = nn.Linear(self.d_model, 3)

    def forward(self, z):
        B = z.size(0)
        memory = self.latent_proj(z).unsqueeze(1)
        tgt = self.point_proxies.expand(B, -1, -1)
        transformer_out = self.transformer_decoder(tgt, memory)
        coarse_points = self.coord_head(transformer_out)
        return coarse_points

class FoldingRefiner(nn.Module):
    def __init__(self, latent_dim, num_points_per_patch=8, d_model=256):
        super().__init__()
        self.num_points_per_patch = num_points_per_patch
        
        # --- CORRECCIÓN IMPORTANTE: Generación de rejilla flexible ---
        # Se genera una rejilla 2D aleatoria y fija para evitar el error del "cuadrado perfecto".
        self.grid = torch.rand(1, self.num_points_per_patch, 2) * 2 - 1 # Puntos aleatorios en [-1, 1]

        self.folding_mlp = nn.Sequential(
            nn.Linear(latent_dim + 3 + 2, d_model),
            nn.ReLU(),
            nn.Linear(d_model, d_model),
            nn.ReLU(),
            nn.Linear(d_model, 3)
        )

    def forward(self, z, coarse_points):
        B, N_coarse, _ = coarse_points.shape
        self.grid = self.grid.to(z.device)
        grid_expanded = self.grid.expand(B, -1, -1)
        z_expanded = z.unsqueeze(1).expand(-1, N_coarse, -1)
        coarse_expanded = coarse_points.unsqueeze(2).expand(-1, -1, self.num_points_per_patch, -1)
        grid_expanded = grid_expanded.unsqueeze(1).expand(-1, N_coarse, -1, -1)
        mlp_input = torch.cat([
            z_expanded.unsqueeze(2).expand(-1, -1, self.num_points_per_patch, -1),
            coarse_expanded,
            grid_expanded
        ], dim=-1)
        offsets = self.folding_mlp(mlp_input)
        dense_points = coarse_expanded + offsets
        return dense_points.view(B, -1, 3)

class PoinTrStyleDecoder(nn.Module):
    def __init__(self, latent_dim, num_points):
        super().__init__()
        self.num_coarse_points = 1024
        self.d_model = 256
        self.nhead = 4
        self.num_decoder_layers = 4
        if num_points % self.num_coarse_points != 0:
            raise ValueError("num_points debe ser divisible por num_coarse_points")
        self.num_points_per_patch = num_points // self.num_coarse_points
        self.generator = TransformerGenerator(
            latent_dim=latent_dim,
            num_coarse_points=self.num_coarse_points,
            d_model=self.d_model,
            nhead=self.nhead,
            num_decoder_layers=self.num_decoder_layers
        )
        self.refiner = FoldingRefiner(
            latent_dim=latent_dim,
            num_points_per_patch=self.num_points_per_patch,
            d_model=self.d_model
        )

    def forward(self, z):
        coarse_points = self.generator(z)
        dense_points = self.refiner(z, coarse_points)
        return dense_points

class PointNetVAE(torch.nn.Module):
    def __init__(self, latent_dim: int, num_points: int):
        super().__init__()
        self.encoder = PointTransformerV3Encoder(latent_dim, input_dim=3)
        self.decoder = PoinTrStyleDecoder(latent_dim, num_points)

    @staticmethod
    def reparameterize(mu, logvar):
        std = torch.exp(0.5 * logvar)
        eps = torch.randn_like(std)
        return mu + eps * std

    def forward(self, x):
        mu, logvar = self.encoder(x)
        # Para la inferencia/visualización, a menudo se usa solo mu. 
        # Pero para mantener la consistencia con la definición del VAE, 
        # el forward completo incluye la reparametrización.
        z = self.reparameterize(mu, logvar)
        recon = self.decoder(z)
        return recon, mu, logvar

# =======================================================================================
# ======================= FIN DE LA SECCIÓN DEL DECODIFICADOR MODIFICADO ======================
# =======================================================================================


# --------------- 运行入口 ---------------- #

def main():
    parser = argparse.ArgumentParser(description="可视化预训练 VAE 的重建效果")
    # ¡Recuerda actualizar esta ruta al checkpoint de tu nuevo modelo entrenado!
    parser.add_argument('--ckpt', type=str, default="/home/<USER>/llm/3DAD/diffusion-point-cloud/logs_vae/VAE_2025_07_28__13_32_48/model_best.pt", help='训练得到的 VAE checkpoint 路径 (.pt)')
    parser.add_argument('--dataset_path', type=str, default="/home/<USER>/llm/3DAD/data/Real3D-AD-PCD", help='Real3D-AD 数据集根目录')
    parser.add_argument('--categories', type=str, default='airplane', help='用逗号分隔的类别名称；all=自动扫描目录')
    parser.add_argument('--split', type=str, default='train', choices=['train','test'])
    parser.add_argument('--num_samples', type=int, default=5, help='要可视化的样本数量')
    parser.add_argument('--device', type=str, default='cuda')
    parser.add_argument('--save_dir', type=str, default='./vae_recon_vis', help='保存 PCD 的目录')
    args = parser.parse_args()

    os.makedirs(args.save_dir, exist_ok=True)

    # ---------- 加载 checkpoint ---------- #
    if not os.path.exists(args.ckpt):
        print(f"Error: El archivo de checkpoint no se encuentra en la ruta: {args.ckpt}")
        print("Por favor, actualiza el argumento --ckpt con la ruta correcta a tu modelo entrenado.")
        return
        
    ckpt = torch.load(args.ckpt, map_location=args.device, weights_only=False)
    ckpt_args = ckpt.get('args', None)
    if ckpt_args:
        print(f"Parámetros cargados desde el checkpoint: num_points = {ckpt_args.num_points}, latent_dim = {ckpt_args.latent_dim}")
        latent_dim = ckpt_args.latent_dim
        num_points = ckpt_args.num_points
    else:
        print("Advertencia: No se encontraron 'args' en el checkpoint. Usando valores por defecto.")
        latent_dim = 256
        num_points = 2048

    model = PointNetVAE(latent_dim, num_points).to(args.device)
    model.load_state_dict(ckpt['state_dict'])
    model.eval()
    print("Modelo cargado exitosamente.")

    # ---------- 数据集 ---------- #
    if args.categories.lower() == 'all':
        cats = [d for d in os.listdir(args.dataset_path) if os.path.isdir(os.path.join(args.dataset_path, d))]
    else:
        cats = [c.strip() for c in args.categories.split(',') if c.strip()]

    # Es crucial usar la misma transformación de alineación que en el entrenamiento/validación
    pca_align = PCARotate(
        attr_keys=['pointcloud'],
        random_flip=False, # False para visualización consistente
        always_align_y_to_gravity=True
    )
    vis_transform = transforms.Compose([
        pca_align
    ])

    dset = Real3DADDataset(
        path=args.dataset_path,
        categories=cats,
        split=args.split,
        scale_mode='shape_bbox',
        transform=vis_transform,
        num_points=num_points,
        sampling_method='hybrid'
    )
    print(f"Dataset cargado: {len(dset)} muestras en la división '{args.split}'.")

    # --------- 遍历样本并保存 PCD --------- #
    for idx in tqdm(range(min(args.num_samples, len(dset))), desc='VAE Recon'):
        data = dset[idx]
        pc = data['pointcloud'].unsqueeze(0).to(args.device)

        with torch.no_grad():
            # Para la visualización, a menudo es más estable usar solo la media 'mu'
            mu, logvar = model.encoder(pc)
            z = mu 
            recon = model.decoder(z).squeeze(0).cpu()

        orig = pc.squeeze(0).cpu()

        # Guardar la nube de puntos original (azul)
        pcd_orig = o3d.geometry.PointCloud()
        pcd_orig.points = o3d.utility.Vector3dVector(orig.numpy())
        pcd_orig.paint_uniform_color([0, 0.65, 0.93]) # Azul
        o3d.io.write_point_cloud(os.path.join(args.save_dir, f'sample_{idx:02d}_orig.pcd'), pcd_orig)

        # Guardar la nube de puntos reconstruida (rojo)
        pcd_recon = o3d.geometry.PointCloud()
        pcd_recon.points = o3d.utility.Vector3dVector(recon.numpy())
        pcd_recon.paint_uniform_color([1, 0.4, 0.4]) # Rojo
        o3d.io.write_point_cloud(os.path.join(args.save_dir, f'sample_{idx:02d}_recon.pcd'), pcd_recon)

    print(f'\n¡Listo! Se han guardado {args.num_samples} pares de nubes de puntos en el directorio: {args.save_dir}')

if __name__ == '__main__':
    main()
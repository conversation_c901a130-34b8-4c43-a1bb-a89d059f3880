import torch
import torch.nn as nn
import torch.nn.functional as F


class PointTransformerV3Encoder(nn.Module):
    """基于Point Transformer V3架构的点云编码器
    
    参考: "Point Transformer V3: Simpler, Faster, Stronger"
    """
    def __init__(self, zdim, input_dim=3):
        """
        Args:
            zdim: 潜在向量维度
            input_dim: 输入点的维度 (默认为3，即xyz坐标)
        """
        super(PointTransformerV3Encoder, self).__init__()
        
        # 基本参数
        self.zdim = zdim
        self.input_dim = input_dim
        
        # 点嵌入层
        self.embedding = nn.Sequential(
            nn.Conv1d(input_dim, 128, 1),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Conv1d(128, 128, 1),
            nn.BatchNorm1d(128),
            nn.ReLU()
        )
        
        # Self-Attention块
        self.sa1 = TransformerBlock(128, 128, 16)
        self.sa2 = TransformerBlock(128, 256, 16)
        self.sa3 = TransformerBlock(256, 512, 16)
        
        # 全局特征
        self.global_conv = nn.Sequential(
            nn.Conv1d(512, 1024, 1),
            nn.BatchNorm1d(1024),
            nn.ReLU()
        )
        
        # 均值预测网络
        self.fc_mean = nn.Sequential(
            nn.Linear(1024, 512),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Linear(256, zdim)
        )
        
        # 方差预测网络
        self.fc_var = nn.Sequential(
            nn.Linear(1024, 512),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Linear(256, zdim)
        )
    
    def forward(self, x):
        """
        Args:
            x: 输入点云 [B, N, 3]
            
        Returns:
            m: 均值向量 [B, zdim]
            v: 对数方差向量 [B, zdim]
        """
        # 输入预处理
        x = x.transpose(1, 2)  # [B, 3, N]
        
        # 点特征嵌入
        x = self.embedding(x)  # [B, 128, N]
        
        # 自注意力处理
        x = self.sa1(x)  # [B, 128, N]
        x = self.sa2(x)  # [B, 256, N]
        x = self.sa3(x)  # [B, 512, N]
        
        # 全局特征提取
        x = self.global_conv(x)  # [B, 1024, N]
        x = torch.max(x, dim=2)[0]  # [B, 1024]
        
        # 预测均值和对数方差
        m = self.fc_mean(x)  # [B, zdim]
        v = self.fc_var(x)   # [B, zdim]
        
        return m, v


class TransformerBlock(nn.Module):
    """Point Transformer V3基本块"""
    def __init__(self, in_dim, out_dim, num_heads):
        """
        Args:
            in_dim: 输入特征维度
            out_dim: 输出特征维度 
            num_heads: 注意力头数
        """
        super(TransformerBlock, self).__init__()
        
        self.in_dim = in_dim
        self.out_dim = out_dim
        self.num_heads = num_heads
        self.head_dim = out_dim // num_heads
        
        # 注意力层
        self.q_conv = nn.Conv1d(in_dim, out_dim, 1, bias=False)
        self.k_conv = nn.Conv1d(in_dim, out_dim, 1, bias=False)
        self.v_conv = nn.Conv1d(in_dim, out_dim, 1, bias=False)
        
        self.attn_norm = nn.BatchNorm1d(out_dim)
        
        # Feed-forward层
        self.ff = nn.Sequential(
            nn.Conv1d(out_dim, out_dim*2, 1),
            nn.BatchNorm1d(out_dim*2),
            nn.ReLU(),
            nn.Conv1d(out_dim*2, out_dim, 1),
            nn.BatchNorm1d(out_dim)
        )
        
        # 输入投影（如果维度不同）
        if in_dim != out_dim:
            self.shortcut = nn.Sequential(
                nn.Conv1d(in_dim, out_dim, 1),
                nn.BatchNorm1d(out_dim)
            )
        else:
            self.shortcut = nn.Identity()
    
    def forward(self, x):
        """
        Args:
            x: 输入特征 [B, C, N]
            
        Returns:
            x: 输出特征 [B, C', N]
        """
        identity = self.shortcut(x)
        
        # Multi-head self-attention
        q = self.q_conv(x)  # [B, C', N]
        k = self.k_conv(x)  # [B, C', N]
        v = self.v_conv(x)  # [B, C', N]
        
        batch_size, C, N = q.shape
        
        # 重塑为多头形式
        q = q.view(batch_size, self.num_heads, self.head_dim, N)  # [B, H, D, N]
        k = k.view(batch_size, self.num_heads, self.head_dim, N)  # [B, H, D, N]
        v = v.view(batch_size, self.num_heads, self.head_dim, N)  # [B, H, D, N]
        
        # 计算注意力
        q = q.permute(0, 1, 3, 2)  # [B, H, N, D]
        k = k.permute(0, 1, 2, 3)  # [B, H, D, N]
        
        attn = torch.matmul(q, k) / (self.head_dim ** 0.5)  # [B, H, N, N]
        attn = F.softmax(attn, dim=-1)
        
        # 应用注意力
        v = v.permute(0, 1, 3, 2)  # [B, H, N, D]
        output = torch.matmul(attn, v)  # [B, H, N, D]
        output = output.permute(0, 1, 3, 2)  # [B, H, D, N]
        output = output.reshape(batch_size, C, N)  # [B, C', N]
        output = self.attn_norm(output)
        
        # 第一个残差连接
        output = output + identity
        
        # Feed-forward层
        ff_out = self.ff(output)
        output = output + ff_out  # 第二个残差连接
        
        return output




import torch
from torch.nn import Module

from .common import *
from .encoders import *
from .diffusion import *


class GaussianVAE(Module):

    def __init__(self, args):
        super().__init__()
        self.args = args
        self.encoder = PointTransformerV3Encoder(zdim=args.latent_dim)
        self.diffusion = DiffusionPoint(
            net = PointwiseNet(point_dim=3, context_dim=args.latent_dim, residual=args.residual),
            var_sched = VarianceSchedule(
                num_steps=args.num_steps,
                beta_1=args.beta_1,
                beta_T=args.beta_T,
                mode=args.sched_mode
            )
        )
        
    def get_loss(self, x, writer=None, it=None, kl_weight=1.0):
        """
        Args:
            x:  Input point clouds, (B, N, d).
        """
        batch_size, _, _ = x.size()
        z_mu, z_sigma = self.encoder(x)
        z = reparameterize_gaussian(mean=z_mu, logvar=z_sigma)  # (B, F)
        log_pz = standard_normal_logprob(z).sum(dim=1)  # (B, ), Independence assumption
        entropy = gaussian_entropy(logvar=z_sigma)      # (B, )
        loss_prior = (- log_pz - entropy).mean()

        loss_recons, recons_components = self.diffusion.get_loss(x, z)
        
        loss = kl_weight * loss_prior + loss_recons

        if writer is not None:
            writer.add_scalar('train/loss_entropy', -entropy.mean(), it)
            writer.add_scalar('train/loss_prior', -log_pz.mean(), it)
            writer.add_scalar('train/loss_recons', loss_recons, it)
            
            # 记录重建损失的各个组成部分
            if recons_components:
                writer.add_scalar('train/recons_mse', recons_components['mse_loss'], it)
                writer.add_scalar('train/recons_l1_reg', recons_components['l1_reg'], it)
                writer.add_scalar('train/recons_l2_reg', recons_components['l2_reg'], it)
                writer.add_scalar('train/recons_tv_reg', recons_components['tv_reg'], it)
                writer.add_scalar('train/recons_total_reg', recons_components['total_reg'], it)

        # 返回总损失和组件
        loss_components = {
            'loss_prior': loss_prior.item(),
            'loss_recons': loss_recons.item(),
            'loss_entropy': -entropy.mean().item(),
            'loss_log_pz': -log_pz.mean().item(),
            'total_loss': loss.item()
        }
        
        # 合并重建损失的组件
        if recons_components:
            for k, v in recons_components.items():
                loss_components[f'recons_{k}'] = v
                
        return loss, loss_components

    def sample(self, z, num_points, flexibility, truncate_std=None):
        """
        Args:
            z:  Input latent, normal random samples with mean=0 std=1, (B, F)
        """
        if truncate_std is not None:
            z = truncated_normal_(z, mean=0, std=1, trunc_std=truncate_std)
        samples = self.diffusion.sample(num_points, context=z, flexibility=flexibility)
        return samples

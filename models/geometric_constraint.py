import torch
import torch.nn.functional as F

class GeometricChecker:
    def __init__(self, k_neighbors=16):
        self.k = k_neighbors
    
    def get_local_density(self, pc):
        """
        计算点云的局部密度
        """
        dist = torch.cdist(pc, pc)  # (B, N, N)
        knn_dist = torch.topk(dist, k=self.k, dim=-1, largest=False)[0]  # (B, N, k)
        density = knn_dist.mean(dim=-1)  # (B, N)
        return density

    def get_normals(self, pc):
        """
        估计点云法向量
        """
        batch_size, num_points, _ = pc.size()
        
        # 计算点云中所有点对之间的距离，形状为 (B, N, N)
        dist = torch.cdist(pc, pc)
        # 找到每个点的 k 个最近邻的索引，形状为 (B, N, k)
        _, idx = torch.topk(dist, k=self.k, dim=-1, largest=False)
        
        # 调整索引形状，准备高级索引
        idx = idx.view(batch_size, -1)  # 形状变为 (B, N*k)
        batch_idx = torch.arange(batch_size).view(-1, 1).repeat(1, num_points * self.k)
        # 利用高级索引提取邻域点，重构为 (B, N, k, 3)
        neighbors = pc[batch_idx, idx].view(batch_size, num_points, self.k, 3)
        # 对邻域点进行中心化
        neighbors_centered = neighbors - neighbors.mean(dim=2, keepdim=True)  # (B, N, k, 3)
        
        # 计算协方差矩阵：对于每个点，用邻域的中心化坐标计算 (3, 3) 的协方差矩阵
        # 这里将最后两个维度交换，得到 (B, N, 3, k) 然后与 (B, N, k, 3) 相乘，
        # 利用 torch.matmul 自动处理高维批次乘法，结果形状为 (B, N, 3, 3)
        cov = torch.matmul(neighbors_centered.transpose(2, 3), neighbors_centered)
        
        # 对协方差矩阵做特征值分解，选取最小特征值对应的特征向量作为法向量
        eigenvalues, eigenvectors = torch.linalg.eigh(cov)
        normals = eigenvectors[..., 0]  # (B, N, 3)
        
        return normals

    def check_geometric_validity(self, pc_normal, pc_anomaly):
        density_normal = self.get_local_density(pc_normal)
        density_anomaly = self.get_local_density(pc_anomaly)
        density_score = F.mse_loss(density_normal, density_anomaly, reduction='none').mean(dim=-1)

        normals_normal = self.get_normals(pc_normal)
        normals_anomaly = self.get_normals(pc_anomaly)
        normal_similarity = torch.abs(torch.sum(normals_normal * normals_anomaly, dim=-1)).mean(dim=-1)

        score = density_score * 0.4 + (1 - normal_similarity) * 0.6
        
        return score

def geometric_consistency_loss(pc_normal, pc_anomaly, checker):
    """
    计算几何一致性损失
    """
    density_normal = checker.get_local_density(pc_normal)
    density_anomaly = checker.get_local_density(pc_anomaly)
    density_loss = F.mse_loss(density_normal, density_anomaly)

    normals_normal = checker.get_normals(pc_normal)
    normals_anomaly = checker.get_normals(pc_anomaly)
    normal_loss = 1 - torch.abs(torch.sum(normals_normal * normals_anomaly, dim=-1)).mean()

    total_loss = density_loss + normal_loss
    
    return total_loss

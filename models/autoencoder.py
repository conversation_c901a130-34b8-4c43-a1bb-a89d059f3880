from torch.nn import Module
import torch

# 假设 PointTransformerV3Encoder 在同级或可通过 Python路径导入
from .encoders.pointnet import PointTransformerV3Encoder # 确认路径
from .diffusion import DiffusionPoint, PointwiseNet, VarianceSchedule
# 从 common.py 导入
from .common import reparameterize_gaussian, gaussian_entropy, standard_normal_logprob


class AutoEncoder(Module):

    def __init__(self, args):
        super().__init__()
        self.args = args
        self.encoder = PointTransformerV3Encoder(zdim=args.latent_dim, input_dim=3) # 假设 input_dim=3

        # 根据是否学习sigma，配置PointwiseNet的output_extra_dim
        self.learn_sigma_diffusion = getattr(args, 'learn_sigma_diffusion', True)  # 从args获取，默认为True

        # ---------- 输入维度 ----------
        # 去掉 mask 通道后，扩散模型的输入就是 xyz (3)
        point_dim_diffusion = 3
        output_extra_dim_diffusion = point_dim_diffusion if self.learn_sigma_diffusion else 0

        # ---------- 缺陷类型嵌入 ----------
        self.num_defect_types = getattr(args, 'num_defect_types', 3)  # 0: normal, 1: sink, 2: bulge
        self.label_emb_dim = getattr(args, 'label_emb_dim', 8)
        self.label_emb = torch.nn.Embedding(self.num_defect_types, self.label_emb_dim)

        # ---------- Diffusion Decoder ----------
        self.diffusion_decoder = DiffusionPoint(
            net=PointwiseNet(
                point_dim=point_dim_diffusion,
                # context = latent_z + label_embedding
                context_dim=args.latent_dim + self.label_emb_dim,
                residual_within_block=getattr(args, 'residual_within_block_diffusion', True),
                time_emb_dim=getattr(args, 'time_emb_dim_diffusion', 128),
                output_extra_dim=output_extra_dim_diffusion
            ),
            var_sched=VarianceSchedule(
                num_steps=args.num_steps,
                beta_1=args.beta_1,
                beta_T=args.beta_T,
                mode=args.sched_mode
            ),
            learn_sigma=self.learn_sigma_diffusion
        )

        # 允许通过 CLI 调整 KL/VLB 权重（Improved-DDPM）
        self.diffusion_decoder.vlb_weight = getattr(args, 'lambda_vlb_diffusion', 1e-3)

    def encode(self, x, return_log_variance=False):
        """
        Args:
            x: Point clouds to be encoded, (B, N, d).
            return_log_variance: 是否返回对数方差。如果为True，返回(均值, 对数方差)；否则只返回均值。
                                 PointTransformerV3Encoder 应该返回 (mean, log_variance)
        """
        mean, log_variance = self.encoder(x) # encoder 直接返回 mean 和 log_variance
        if return_log_variance:
            return mean, log_variance
        return mean

    def decode(self, code, num_points, ret_traj=False, sample_steps=None):
        """
        解码潜在编码为点云

        参数:
            code: (B, latent_dim) 或 (B, latent_dim + label_emb_dim)
            num_points: int, 要生成的点数量
            ret_traj: bool, 是否返回轨迹
            sample_steps: int, 可选，用于控制采样步数 (如果为None，则使用扩散模型的总步数)
        """
        # ------------------------------
        # 如果仅提供 latent (B, latent_dim)，自动附加 "normal" 标签嵌入；
        # 否则假设调用者已拼接了正确长度的 label embedding。
        # ------------------------------
        if code.dim() != 2:
            raise ValueError(f"code tensor 应为二维 (B,D)，但得到形状 {code.shape}")

        expected_latent_dim = self.args.latent_dim
        if code.size(1) == expected_latent_dim:
            # 默认 defect_type = 0 对应 normal
            normal_idx = torch.zeros(code.size(0), dtype=torch.long, device=code.device)
            label_vec = self.label_emb(normal_idx)  # (B, label_emb_dim)
            context = torch.cat([code, label_vec], dim=1)
        else:
            context = code

        return self.diffusion_decoder.sample(
            num_points=num_points,
            context=context,
            batch_size=context.size(0),
            device=context.device,
            point_dim=self.diffusion_decoder.point_dim,  # 保证与训练一致 (3)
            ret_traj=ret_traj,
            sample_steps=sample_steps
        )

    def get_loss(self, x, kl_weight=1.0, use_vae=False):
        """
        计算自编码器的损失

        Args:
            x: 输入点云，形状为 (B, N, 3)
            kl_weight: KL散度项的权重 (仅当 use_vae=True 时使用)
            use_vae: 是否使用变分自编码器的特性

        Returns:
            loss: 总损失值
            loss_components: 包含各部分损失的字典
        """
        loss_components = {}
        kl_div = torch.tensor(0.0, device=x.device)

        if use_vae:
            mean, log_var = self.encode(x, return_log_variance=True)
            # 使用重参数技巧从 q(z|x) 中采样潜编码 z
            code = reparameterize_gaussian(mean, log_var)

            # 计算KL散度: D_KL(q(z|x) || p(z))
            # p(z) 通常是标准正态分布 N(0, I)
            # KL = -0.5 * sum(1 + log(sigma^2) - mu^2 - sigma^2)
            # 或者: KL = E_q[log q(z|x) - log p(z)] = -H[q(z|x)] - E_q[log p(z)]
            # H[q(z|x)] = gaussian_entropy(logvar=log_var)
            # E_q[log p(z)] = E_eps[standard_normal_logprob(mu + sigma*eps)]
            # 由于 E_q[log p(z)] 难以直接计算闭式解（除非p(z)和q(z|x)都是高斯），
            # 通常我们计算蒙特卡洛估计，或者使用其本身的KL散度闭式解（如果p(z)是标准高斯）：
            # KL(q(z|x) || N(0,I)) = -0.5 * torch.sum(1 + log_var - mean.pow(2) - log_var.exp(), dim=1)
            # 我们需要对每个样本求和，然后对批次取平均
            kl_div_per_sample = -0.5 * torch.sum(1 + log_var - mean.pow(2) - torch.exp(log_var), dim=1)
            kl_div = kl_div_per_sample.mean()
            loss_components['kl_divergence'] = kl_div.item()
        else:
            # 标准AE：潜编码是确定性的均值
            code = self.encode(x, return_log_variance=False) # 只获取均值

        # 计算扩散模型的重建损失 (L_rec)
        # diffusion_decoder.get_loss 应该返回 (total_diffusion_loss, diffusion_loss_components)
        expected_latent_dim = self.args.latent_dim
        if code.dim() != 2:
            raise ValueError(f"code tensor 应为二维 (B,D)，但得到形状 {code.shape}")
        if code.size(1) == expected_latent_dim:
            normal_idx = torch.zeros(code.size(0), dtype=torch.long, device=code.device)
            label_vec = self.label_emb(normal_idx)  # (B, label_emb_dim)
            context_for_loss = torch.cat([code, label_vec], dim=1)
        else:
            context_for_loss = code
        reconstruction_loss, diffusion_components = self.diffusion_decoder.get_loss(x_0=x, context=context_for_loss)
        loss_components.update(diffusion_components) # 合并扩散模型的损失组件
        loss_components['reconstruction_loss_diffusion'] = reconstruction_loss.item()


        # 总损失
        total_loss = reconstruction_loss
        if use_vae:
            total_loss += kl_weight * kl_div
            loss_components['weighted_kl_divergence'] = (kl_weight * kl_div).item()

        loss_components['total_vae_loss_or_ae_recons_loss'] = total_loss.item()

        return total_loss, loss_components
from torch.nn import Mo<PERSON>le
import torch

from .encoders.pointnet import PointNet2Encoder 
# -------------------------------------------

from .diffusion import DiffusionPoint, PointwiseNet, VarianceSchedule
from .common import reparameterize_gaussian, gaussian_entropy, standard_normal_logprob


class AutoEncoder(Module):

    def __init__(self, args):
        super().__init__()
        self.args = args

        self.encoder = PointNet2Encoder(zdim=args.latent_dim, input_dim=3) 
        # ---------------------------------------

        # 根据是否学习sigma，配置PointwiseNet的output_extra_dim
        self.learn_sigma_diffusion = getattr(args, 'learn_sigma_diffusion', True)  # 从args获取，默认为True

        # ---------- 输入维度 ----------
        # 去掉 mask 通道后，扩散模型的输入就是 xyz (3)
        point_dim_diffusion = 3
        output_extra_dim_diffusion = point_dim_diffusion if self.learn_sigma_diffusion else 0

        # ---------- 缺陷类型嵌入 ----------
        self.num_defect_types = getattr(args, 'num_defect_types', 3)  # 0: normal, 1: bulge, 2: sink
        # 变量名修正：uncod -> uncond
        self.uncond_label_id = self.num_defect_types 
        self.label_emb_dim = getattr(args, 'label_emb_dim', 8)
        self.label_emb = torch.nn.Embedding(self.num_defect_types + 1, self.label_emb_dim)

        # ---------- Diffusion Decoder ----------
        self.diffusion_decoder = DiffusionPoint(
            net=PointwiseNet(
                point_dim=point_dim_diffusion,
                context_dim=args.latent_dim + self.label_emb_dim,
                residual_within_block=getattr(args, 'residual_within_block_diffusion', True),
                time_emb_dim=getattr(args, 'time_emb_dim_diffusion', 128),
                output_extra_dim=output_extra_dim_diffusion
            ),
            var_sched=VarianceSchedule(
                num_steps=args.num_steps,
                beta_1=args.beta_1,
                beta_T=args.beta_T,
                mode=args.sched_mode
            ),
            learn_sigma=self.learn_sigma_diffusion
        )

        # 允许通过 CLI 调整 KL/VLB 权重（Improved-DDPM）
        self.diffusion_decoder.vlb_weight = getattr(args, 'lambda_vlb_diffusion', 1e-3)

    def encode(self, x, return_log_variance=False):
        """
        Args:
            x: Point clouds to be encoded, (B, N, d).
            return_log_variance: 是否返回对数方差。如果为True，返回(均值, 对数方差)；否则只返回均值。
        """
        # PointNet2Encoder的输出已经是 (mean, log_variance)
        mean, log_variance = self.encoder(x)
        if return_log_variance:
            return mean, log_variance
        return mean

    def decode(self, code, num_points, ret_traj=False, sample_steps=None, guidance_scale=1.0):
        """
        解码潜在编码为点云，支持Classifier-Free Guidance。
        """
        B, D = code.shape
        latent_z = code

        # --- CFG MODIFICATION: 准备有条件和无条件的上下文 ---
        is_guided = guidance_scale > 1.0

        # 1. 准备有条件的上下文 (默认是 'normal' 标签)
        if D == self.args.latent_dim:
            normal_idx = torch.zeros(B, dtype=torch.long, device=code.device)
            label_vec_cond = self.label_emb(normal_idx)
            context_cond = torch.cat([latent_z, label_vec_cond], dim=1)
        else: # 假设调用者已传入拼接好的上下文
            context_cond = code

        if is_guided:
            # 2. 准备无条件的上下文
            uncond_label_id = self.uncond_label_id
            uncond_idx = torch.full((B,), uncond_label_id, dtype=torch.long, device=code.device)
            label_vec_uncond = self.label_emb(uncond_idx)

            # 确保 latent_z 部分是正确的
            if D != self.args.latent_dim:
                latent_z = code[:, :self.args.latent_dim] # 从拼接好的context中提取z
            
            context_uncond = torch.cat([latent_z, label_vec_uncond], dim=1)

            # 3. 将两者拼接成一个大的上下文张量
            context = torch.cat([context_cond, context_uncond], dim=0)
        else:
            context = context_cond
        # --- CFG MODIFICATION: END ---

        return self.diffusion_decoder.sample(
            num_points=num_points,
            context=context,
            batch_size=B, # 真实批次大小
            device=context.device,
            point_dim=self.diffusion_decoder.point_dim,
            ret_traj=ret_traj,
            sample_steps=sample_steps,
            guidance_scale=guidance_scale # 将引导强度传递给sample函数
        )
        
    def get_loss(self, x, kl_weight=1.0, use_vae=False):
        """
        计算自编码器的损失
        (这部分代码无需修改，因为它与Encoder的具体实现解耦)
        """
        loss_components = {}
        kl_div = torch.tensor(0.0, device=x.device)

        if use_vae:
            mean, log_var = self.encode(x, return_log_variance=True)
            code = reparameterize_gaussian(mean, log_var)
            
            kl_div_per_sample = -0.5 * torch.sum(1 + log_var - mean.pow(2) - torch.exp(log_var), dim=1)
            kl_div = kl_div_per_sample.mean()
            loss_components['kl_divergence'] = kl_div.item()
        else:
            code = self.encode(x, return_log_variance=False)

        # 计算扩散模型的重建损失 (L_rec)
        expected_latent_dim = self.args.latent_dim
        if code.dim() != 2:
            raise ValueError(f"code tensor 应为二维 (B,D)，但得到形状 {code.shape}")
        
        if code.size(1) == expected_latent_dim:
            normal_idx = torch.zeros(code.size(0), dtype=torch.long, device=code.device)
            label_vec = self.label_emb(normal_idx)
            context_for_loss = torch.cat([code, label_vec], dim=1)
        else:
            context_for_loss = code
            
        reconstruction_loss, diffusion_components = self.diffusion_decoder.get_loss(x_0=x, context=context_for_loss)
        loss_components.update(diffusion_components)
        loss_components['reconstruction_loss_diffusion'] = reconstruction_loss.item()

        # 总损失
        total_loss = reconstruction_loss
        if use_vae:
            total_loss += kl_weight * kl_div
            loss_components['weighted_kl_divergence'] = (kl_weight * kl_div).item()

        loss_components['total_vae_loss_or_ae_recons_loss'] = total_loss.item()

        return total_loss, loss_components
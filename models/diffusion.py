import math
import torch
import torch.nn.functional as F
import numpy as np
from torch.nn import Module
import torch.nn as nn
from tqdm.auto import tqdm  

class SinusoidalPositionEmbeddings(nn.Module):
    def __init__(self, dim):
        super().__init__()
        self.dim = dim

    def forward(self, time):
        device = time.device
        half_dim = self.dim // 2
        emb = math.log(10000) / (half_dim - 1)
        emb = torch.exp(torch.arange(half_dim, device=device) * -emb)
        emb = time[:, None] * emb[None, :]
        emb = torch.cat((emb.sin(), emb.cos()), dim=-1)
        if self.dim % 2 == 1:  # zero pad if dim is odd
            emb = F.pad(emb, (0, 1), mode='constant')
        return emb
class VarianceSchedule(Module):
    r"""
    生成 DDPM / Improved‑DDPM 训练与采样所需的 β_t、ᾱ_t 等缓冲区。
    支持线性和余弦两种 β_t 生成模式。
    """

    def __init__(self,
                 num_steps: int,
                 beta_1: float = 1e-4,
                 beta_T: float = 0.02,
                 mode: str = 'cosine'):
        """
        Args:
            num_steps:  时间步数 T。
            beta_1, beta_T: 仅在 `mode='linear'` 时生效；线性插值的端点。
            mode:  'linear' 或 'cosine'。
        """
        super().__init__()
        assert mode in ('linear', 'cosine'), "mode must be 'linear' or 'cosine'"
        self.num_steps = num_steps
        self.mode = mode

        # -------------------------------------------------
        # 1. 生成 beta_t 序列（索引从 1 开始；填充 0 作为占位）
        # -------------------------------------------------
        if mode == 'linear':
            betas = torch.linspace(beta_1, beta_T, steps=num_steps, dtype=torch.float32)
        else:                          # cosine 方案 (Nichol & Dhariwal, 2021)
            s = 0.008
            steps_f = torch.arange(0, num_steps + 1, dtype=torch.float64)  # 0 … T
            alphas_cumprod = torch.cos(((steps_f / num_steps + s) / (1 + s)) * (math.pi / 2)) ** 2
            alphas_cumprod = alphas_cumprod / alphas_cumprod[0]

            betas = 1.0 - (alphas_cumprod[1:] / alphas_cumprod[:-1])
            betas = betas.clamp_(0., 0.999).to(torch.float32)

        betas = torch.cat([torch.zeros(1, dtype=torch.float32), betas])     # (T+1,)  β₀ = 0
        alphas = 1.0 - betas                                                # α_t
        log_alphas = torch.log(alphas.clamp(min=1e-40))
        alpha_bars = torch.exp(torch.cumsum(log_alphas, dim=0))             # ᾱ_t
        alpha_bars[0] = 1.0                                                 # ᾱ₀ = 1

        # -------------------------------------------------
        # 2. 计算后验方差  \tilde{β}_t
        #    (1 - ᾱ_{t-1}) / (1 - ᾱ_t) * β_t
        # -------------------------------------------------
        alpha_bars_prev = torch.cat([torch.tensor([1.0], dtype=torch.float32, device=alpha_bars.device), alpha_bars[:-1]])
        posterior_variance = (1.0 - alpha_bars_prev) / (1.0 - alpha_bars.clamp(min=1e-20)) * betas

        # 修正关键 bug：确保 \tilde{β}_1 = β_1 而不是 0
        posterior_variance[1] = betas[1]

        # 为 t=0 填一个安全值（不参与训练/采样，仅占位）
        posterior_variance[0] = posterior_variance[1]

        posterior_variance_clipped = posterior_variance.clone()
        log_posterior_variance = torch.log(posterior_variance_clipped.clamp(min=1e-20))

        # -------------------------------------------------
        # 3. 注册为 buffer，方便在 GPU/CPU 间自动同步
        # -------------------------------------------------
        self.register_buffer('betas', betas)
        self.register_buffer('alphas', alphas)
        self.register_buffer('alpha_bars', alpha_bars)
        self.register_buffer('log_alpha_bars', torch.log(alpha_bars.clamp(min=1e-40)))
        self.register_buffer('sqrt_alpha_bars', torch.sqrt(alpha_bars))
        self.register_buffer('sqrt_one_minus_alpha_bars', torch.sqrt(1.0 - alpha_bars))
        self.register_buffer('posterior_variance', posterior_variance_clipped)
        self.register_buffer('log_posterior_variance', log_posterior_variance)

    # -----------------------------------------------------
    # 4. 采样一个整数时间步 t ∈ [1, T]
    # -----------------------------------------------------
    def uniform_sample_t(self, batch_size: int) -> torch.Tensor:
        return torch.randint(1, self.num_steps + 1, (batch_size,), device=self.betas.device)

class PointwiseNet(Module):
    def __init__(self, point_dim, context_dim=None, residual_within_block=True, num_heads=4, time_emb_dim=128, output_extra_dim=0):
        super().__init__()
        self.act = nn.SiLU()
        # self.residual = residual # Removed: residual connection at the end is not typical for noise prediction
        self.residual_within_block = residual_within_block # For attention blocks
        self.context_dim = context_dim
        self.num_heads = num_heads
        self.time_emb_dim = time_emb_dim

        self.time_mlp = SinusoidalPositionEmbeddings(self.time_emb_dim)

        # 计算嵌入维度 (时间嵌入 + context_dim如果存在)
        # Context embed dim adjusted for time_mlp output
        ctx_input_dim = self.time_emb_dim
        if context_dim is not None:
            ctx_input_dim += context_dim

        # 点特征提取层
        self.point_embedding = nn.Sequential(
            nn.Linear(point_dim, 64),
            nn.LayerNorm(64),
            nn.SiLU(),
            nn.Linear(64, 128),
            nn.LayerNorm(128),
            nn.SiLU()
        )

        # 时间和上下文嵌入层
        self.context_embedding_mlp = nn.Sequential(
            nn.Linear(ctx_input_dim, 128), # Match point_features dim
            nn.LayerNorm(128),
            nn.SiLU(),
            nn.Linear(128, 128), # Added another layer
            nn.LayerNorm(128),
            nn.SiLU()
        )

        # 局部注意力层
        self.local_attention_blocks = nn.ModuleList([
            SelfAttentionBlock(128, self.num_heads, residual=self.residual_within_block),
            SelfAttentionBlock(128, self.num_heads, residual=self.residual_within_block)
        ])

        # 全局注意力层（处理局部特征和上下文）
        self.global_attention_blocks = nn.ModuleList([
            CrossAttentionBlock(128, 128, self.num_heads, residual=self.residual_within_block),
            CrossAttentionBlock(128, 128, self.num_heads, residual=self.residual_within_block)
        ])

        # 输出层: point_dim for noise + output_extra_dim for variance parameters
        self.output_layers = nn.Sequential(
            nn.Linear(128, 128),
            nn.LayerNorm(128),
            nn.SiLU(),
            nn.Linear(128, 64),
            nn.LayerNorm(64),
            nn.SiLU(),
            nn.Linear(64, point_dim + output_extra_dim) # Predict noise (point_dim) + variance params
        )

    def forward(self, x, time_steps, context=None):
        """
        Args:
            x:  Point clouds at some timestep t, (B, N, d).
            time_steps: Time steps t. (B, ).
            context:  Shape latent, (B, F).
        """
        batch_size, num_points, _ = x.size()

        # 时间嵌入 using SinusoidalPositionEmbeddings
        time_emb = self.time_mlp(time_steps) # (B, time_emb_dim)
        time_emb = time_emb.unsqueeze(1) # (B, 1, time_emb_dim)

        # 如果提供了context，则与时间嵌入拼接
        if context is not None and self.context_dim is not None:
            context = context.view(batch_size, 1, -1)   # (B, 1, F)
            ctx_emb_input = torch.cat([time_emb, context], dim=-1)    # (B, 1, F + time_emb_dim)
        else:
            ctx_emb_input = time_emb    # 只使用时间嵌入

        # 点特征嵌入
        point_features = self.point_embedding(x)  # (B, N, 128)

        # 上下文嵌入
        # ctx_emb_input is (B, 1, ctx_input_dim)
        context_features = self.context_embedding_mlp(ctx_emb_input)  # (B, 1, 128)
        context_features = context_features.expand(-1, num_points, -1)  # (B, N, 128)

        current_features = point_features
        # 首先应用局部自注意力
        for attn_block in self.local_attention_blocks:
            current_features = attn_block(current_features) # Residual is inside the block

        # 然后应用全局交叉注意力（结合上下文）
        for attn_block in self.global_attention_blocks:
            current_features = attn_block(current_features, context_features) # Residual is inside the block

        # 最终输出层
        out = self.output_layers(current_features)  # (B, N, point_dim + output_extra_dim)

        # No final residual connection x + out here, as we predict noise or its parameters
        return out


class SelfAttentionBlock(nn.Module):
    def __init__(self, dim, num_heads, residual=True):
        super().__init__()
        self.norm = nn.LayerNorm(dim)
        self.attention = nn.MultiheadAttention(dim, num_heads, batch_first=True)
        self.ff = nn.Sequential(
            nn.Linear(dim, dim * 4),
            nn.GELU(),
            nn.Linear(dim * 4, dim)
        )
        self.ff_norm = nn.LayerNorm(dim)
        self.residual = residual

    def forward(self, x):
        identity = x
        x_norm = self.norm(x)
        attn_out, _ = self.attention(x_norm, x_norm, x_norm)
        if self.residual:
            x = identity + attn_out
        else:
            x = attn_out
        
        identity_ff = x
        x_norm = self.ff_norm(x)
        ff_out = self.ff(x_norm)
        if self.residual:
            x = identity_ff + ff_out
        else:
            x = ff_out
        return x

class CrossAttentionBlock(nn.Module):
    def __init__(self, q_dim, kv_dim, num_heads, residual=True):
        super().__init__()
        self.norm_q = nn.LayerNorm(q_dim)
        self.norm_kv = nn.LayerNorm(kv_dim)
        self.attention = nn.MultiheadAttention(q_dim, num_heads, batch_first=True, kdim=kv_dim, vdim=kv_dim)
        self.ff = nn.Sequential(
            nn.Linear(q_dim, q_dim * 4),
            nn.GELU(),
            nn.Linear(q_dim * 4, q_dim)
        )
        self.ff_norm = nn.LayerNorm(q_dim)
        self.residual = residual

    def forward(self, q, kv):
        identity_q = q
        q_norm = self.norm_q(q)
        kv_norm = self.norm_kv(kv)
        attn_out, _ = self.attention(q_norm, kv_norm, kv_norm)
        if self.residual:
            q = identity_q + attn_out
        else:
            q = attn_out
        
        identity_ff_q = q
        q_norm = self.ff_norm(q)
        ff_out = self.ff(q_norm)
        if self.residual:
            q = identity_ff_q + ff_out
        else:
            q = ff_out
        return q


class DiffusionPoint(Module):
    def __init__(self, net: PointwiseNet, var_sched: VarianceSchedule, learn_sigma: bool = True):
        super().__init__()
        self.net = net
        self.var_sched = var_sched
        self.learn_sigma = learn_sigma

        # ----- Infer point_dim from network output layer -----
        out_dim = self.net.output_layers[-1].out_features
        if self.learn_sigma:
            # output = [eps_pred (D), var_param (D)] => total 2D
            self.point_dim = out_dim // 2
            assert out_dim == self.point_dim * 2, "For learn_sigma=True, network output should be 2*point_dim"
        else:
            self.point_dim = out_dim

    def _get_model_output_split(self, model_output):
        if self.learn_sigma:
            return model_output.split(self.point_dim, dim=-1) # (pred_noise, pred_v_param)
        else:
            return model_output, None # (pred_noise, None)

    def _get_learned_log_sigma_t(self, pred_v_param, t):
        # pred_v_param is the network's output for variance control (B, N, point_dim)
        # It parameterizes the model's learned variance.
        # From Improved DDPM paper (eq. 13), model_log_variance = v * log(beta_t) + (1-v) * log(\tilde{beta}_t)
        # where v is learned by the network, clamped to [0,1] (often via sigmoid).
        # Here, pred_v_param can be considered 'v' after a sigmoid.
        
        # We need log_beta_t and log_posterior_variance_t (\tilde{beta}_t)
        # These should be available from var_sched, possibly needing expansion for batch and points
        batch_size = pred_v_param.shape[0]
        num_points = pred_v_param.shape[1]

        log_beta_t = torch.log(self.var_sched.betas[t]).view(batch_size, 1, 1).expand(-1, num_points, self.point_dim)
        log_tilde_beta_t = self.var_sched.log_posterior_variance[t].view(batch_size, 1, 1).expand(-1, num_points, self.point_dim)
        
        # pred_v_param is the raw output from the network, let's treat it as the 'v' input to the formula.
        # The original paper's 'v' is a scalar, but here we output per-dimension.
        # For simplicity, let's apply a sigmoid to pred_v_param to map it to [0,1]
        # This assumes pred_v_param has the same dim as point_dim.
        v = torch.sigmoid(pred_v_param) # (B, N, point_dim)
        
        learned_log_sigma_sq = v * log_beta_t + (1 - v) * log_tilde_beta_t
        return learned_log_sigma_sq # This is log(sigma_t^2)

    def get_loss(self, x_0, context=None, t=None):
        """
        Improved DDPM Loss L_vlb (simplified for L_hybrid from paper)
        L_simple = E_t,x_0,eps [ || eps - eps_theta(x_t, t) ||^2 ]
        L_vlb = E_t [ L_t ]
        L_t = {
            L_0 for t=1 (special nll term for p_θ(x_0|x_1))
            D_KL(q(x_{t-1}|x_t, x_0) || p_θ(x_{t-1}|x_t)) for t > 1
        }
        这里实现完整 KL（同 Improved-DDPM 公式 15），并用常量 `vlb_weight`（默认 1e-3）
        对其加权并与 SNR-weighted MSE(ε) 相加。
        """
        batch_size, num_points, point_dim = x_0.size()
        if t is None:
            t = self.var_sched.uniform_sample_t(batch_size) # Sample t from [1, T]

        # q(x_t | x_0)
        sqrt_alpha_bar_t = self.var_sched.sqrt_alpha_bars[t].view(-1, 1, 1)
        sqrt_one_minus_alpha_bar_t = self.var_sched.sqrt_one_minus_alpha_bars[t].view(-1, 1, 1)
        noise_gt = torch.randn_like(x_0)
        x_t = sqrt_alpha_bar_t * x_0 + sqrt_one_minus_alpha_bar_t * noise_gt

        # Predict noise (eps_theta) and variance parameters using the network
        model_output = self.net(x_t, time_steps=t, context=context)
        pred_noise_eps, pred_v_param = self._get_model_output_split(model_output)

        # 2. 计算 SNR-weighted MSE(ε)
        # SNR = ᾱ / (1-ᾱ) ，公式来自 Stable Diffusion v2 训练
        alpha_bar_t = self.var_sched.alpha_bars[t]
        alpha_t_full = self.var_sched.alphas[t]
        alpha_t = alpha_t_full.view(-1,1,1)
        sqrt_alpha_t = torch.sqrt(alpha_t)
        snr_t = alpha_bar_t / (1 - alpha_bar_t)
        # 使用 gamma=5 的截断权重  w = min(γ/snr, 1)
        gamma = 5.0
        w_t = torch.minimum(gamma / snr_t, torch.ones_like(snr_t))  # (B,)

        mse_loss_unred = F.mse_loss(pred_noise_eps, noise_gt, reduction='none').mean(dim=(1, 2))  # (B,)
        mse_loss = (w_t * mse_loss_unred).mean()  # 标量

        # ------------------------------
        # 计算完整 KL(q||p_θ)             
        # ------------------------------
        # 1. 预测 x0 以计算模型均值 μ_θ
        x0_pred = (x_t - sqrt_one_minus_alpha_bar_t * pred_noise_eps) / sqrt_alpha_bar_t  # (B,N,D)

        # 2. 计算真后验 μ_q = coef1 * x0_true + coef2 * x_t
        #    和模型后验 μ_θ = coef1 * x0_pred + coef2 * x_t
        #    系数参见 DDPM 公式 (12)
        alpha_bars_prev = self.var_sched.alpha_bars[(t-1).clamp(min=0)]  # (B,)

        beta_t = self.var_sched.betas[t].view(-1,1,1)
        sqrt_alpha_bar_prev = torch.sqrt(alpha_bars_prev).view(-1,1,1)
        one_minus_alpha_bar_t = (1.0 - alpha_bar_t).view(-1,1,1)

        coef1 = (sqrt_alpha_bar_prev * beta_t) / one_minus_alpha_bar_t  # (B,1,1)
        coef2 = (sqrt_alpha_t * (1.0 - alpha_bars_prev).view(-1,1,1)) / one_minus_alpha_bar_t  # (B,1,1)

        true_mean = coef1 * x_0 + coef2 * x_t           # (B,N,D)
        model_mean = coef1 * x0_pred + coef2 * x_t      # (B,N,D)

        # 3. 方差 log σ²：真值为 log β̃_t，模型为 learned
        log_posterior_variance_t = self.var_sched.log_posterior_variance[t].view(-1,1,1)
        log_posterior_variance_t_exp = log_posterior_variance_t.expand_as(true_mean)
        if self.learn_sigma:
            log_model_variance = self._get_learned_log_sigma_t(pred_v_param, t)  # (B,N,D)
        else:
            log_model_variance = log_posterior_variance_t_exp   # 固定

        # 4. KL per-element
        kl_per = 0.5 * (
            torch.exp(log_model_variance - log_posterior_variance_t_exp) +
            (true_mean - model_mean) ** 2 / torch.exp(log_posterior_variance_t_exp) -
            1.0 + log_posterior_variance_t_exp - log_model_variance
        )  # (B,N,D)

        kl_loss = kl_per.mean()  # 标量

        # ------------------------------
        # 总损失
        # ------------------------------
        vlb_weight = getattr(self, 'vlb_weight', 1e-3)
        total_loss = mse_loss + vlb_weight * kl_loss
            
        loss_components = {
            'mse_eps_snr': mse_loss.detach(),
            'kl_full': kl_loss.detach(),
            'vlb_weight': vlb_weight
        }

        return total_loss, loss_components

    def sample(self, num_points, context, batch_size=None, device=None, point_dim=3, ret_traj=False, sample_steps=None, guidance_scale=1.0):
        if batch_size is None:
            batch_size = context.size(0)
        if device is None:
            device = context.device

        # --- CFG MODIFICATION: 如果启用引导，batch_size会临时加倍 ---
        is_guided = guidance_scale > 1.0
        if is_guided:
            # context 应该是 [cond_context, uncond_context] 拼接而成
            # 真实批次大小是 context 的一半
            batch_size = context.size(0) // 2
        # --- CFG MODIFICATION: END ---

        x_T = torch.randn([batch_size, num_points, point_dim]).to(device)
        traj = {self.var_sched.num_steps: x_T.cpu()}

        time_seq = range(self.var_sched.num_steps, 0, -1) if sample_steps is None else \
                   torch.linspace(self.var_sched.num_steps, 1, steps=sample_steps).long().tolist()

        x_t = x_T
        for t_int in tqdm(time_seq, desc="Diffusion Sampling", leave=False, disable=(torch.dist.get_rank() != 0 if torch.dist.is_initialized() else False)):
            t = torch.full((batch_size,), t_int, device=device, dtype=torch.long)
            
            # --- CFG MODIFICATION: 准备模型输入 ---
            if is_guided:
                # 将 x_t 复制一份，分别对应有条件和无条件
                model_input = x_t.repeat(2, 1, 1)
                t_input = t.repeat(2)
                context_input = context # context 已经是拼接好的
            else:
                model_input = x_t
                t_input = t
                context_input = context
            # --- CFG MODIFICATION: END ---

            # --- 模型预测 ---
            model_output = self.net(model_input, time_steps=t_input, context=context_input)
            pred_noise_eps, pred_v_param = self._get_model_output_split(model_output)
            # --- 模型预测结束 ---

            # --- CFG MODIFICATION: 应用引导 ---
            if is_guided:
                # 将输出拆分为有条件和无条件两部分
                eps_cond, eps_uncond = pred_noise_eps.chunk(2, dim=0)
                # 应用CFG公式
                final_pred_noise_eps = eps_uncond + guidance_scale * (eps_cond - eps_uncond)
                
                # 方差参数通常只使用有条件的部分
                if pred_v_param is not None:
                    pred_v_param, _ = pred_v_param.chunk(2, dim=0)
            else:
                final_pred_noise_eps = pred_noise_eps
            # --- CFG MODIFICATION: END ---

            z = torch.randn_like(x_t) if t_int > 1 else torch.zeros_like(x_t)
            
            alpha_t = self.var_sched.alphas[t].view(-1,1,1)
            sqrt_alpha_t = torch.sqrt(alpha_t)
            beta_t = self.var_sched.betas[t].view(-1,1,1)
            sqrt_one_minus_alpha_bar_t = self.var_sched.sqrt_one_minus_alpha_bars[t].view(-1,1,1)
            
            # 计算 sigma_t (方差)
            if self.learn_sigma:
                log_model_variance_t = self._get_learned_log_sigma_t(pred_v_param, t)
                sigma_t = torch.exp(0.5 * log_model_variance_t)
            else:
                log_posterior_variance_t_expanded = self.var_sched.log_posterior_variance[t].view(batch_size,1,1).expand_as(x_t)
                sigma_t = torch.exp(0.5 * log_posterior_variance_t_expanded)
            
            # 使用最终的噪声预测进行采样
            x_t_minus_1 = (1.0 / sqrt_alpha_t) * (x_t - (beta_t / sqrt_one_minus_alpha_bar_t) * final_pred_noise_eps) + sigma_t * z
            x_t = x_t_minus_1.detach()

            if ret_traj and (t_int % 50 == 0 or t_int <= 10):
                traj[t_int-1] = x_t.cpu()

        return x_t.cpu()
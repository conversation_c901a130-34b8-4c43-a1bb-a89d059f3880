#!/usr/bin/env python
"""
为数据集中的每个点云生成并可视化缺陷样本。
(V3: 显著增强缺陷强度，并增加多样性)
"""
import os
import argparse
import torch
import random
import open3d as o3d
import numpy as np
from tqdm.auto import tqdm

import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

from utils.dataset import Real3DADDataset
from utils.data import custom_collate_fn, DataLoader
from utils.defect_augmentation import RealisticDefectAugmenter
from utils.misc import seed_all

def tensor_to_pcd(tensor: torch.Tensor) -> o3d.geometry.PointCloud:
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(tensor.cpu().numpy())
    return pcd

def setup_3d_axis(ax, x_lim, y_lim, z_lim, elev=20, azim=-60):
    ax.set_xlim(x_lim)
    ax.set_ylim(y_lim)
    ax.set_zlim(z_lim)
    ax.view_init(elev=elev, azim=azim)
    ax.set_xticks([])
    ax.set_yticks([])
    ax.set_zticks([])
    ax.xaxis.pane.fill = False
    ax.yaxis.pane.fill = False
    ax.zaxis.pane.fill = False
    ax.xaxis.pane.set_edgecolor('gray')
    ax.yaxis.pane.set_edgecolor('gray')
    ax.zaxis.pane.set_edgecolor('gray')
    ax.xaxis.pane.set_alpha(0.1)
    ax.yaxis.pane.set_alpha(0.1)
    ax.zaxis.pane.set_alpha(0.1)
    ax.set_aspect('auto')


def main(args):
    seed_all(42)
    plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'SimHei', 'Arial Unicode MS']
    plt.rcParams['axes.unicode_minus'] = False
    os.makedirs(args.output_dir, exist_ok=True)

    augmenter = RealisticDefectAugmenter(device=args.device)
    print(f"初始化 RealisticDefectAugmenter，使用设备: {args.device}")
    
    # --- 打印将要使用的缺陷参数 ---
    print("\n--- 缺陷生成参数 ---")
    print(f"缺陷半径范围 (占包围盒比例): {args.radius_frac}")
    print(f"最大位移范围 (占包围盒比例): {args.disp_frac}")
    print("---------------------\n")


    dset = Real3DADDataset(
        path=args.dataset_path,
        categories=args.categories,
        split='train',
        scale_mode='shape_bbox',
        transform=None,
        num_points=args.num_points
    )
    loader = DataLoader(dset, batch_size=1, shuffle=False, num_workers=4, collate_fn=custom_collate_fn)
    
    if len(loader) == 0:
        print("错误: 数据加载器为空，请检查数据集路径和类别设置。")
        return

    print(f"数据加载完成，共找到 {len(loader)} 个点云文件。")

    for i, batch in enumerate(tqdm(loader, desc='生成缺陷样本')):
        pc_tensor = batch['pointcloud'].to(args.device)[0]
        original_filename = batch['filename'][0]
        
        file_id = os.path.splitext(original_filename)[0]
        sample_output_dir = os.path.join(args.output_dir, file_id)
        os.makedirs(sample_output_dir, exist_ok=True)
        
        o3d.io.write_point_cloud(os.path.join(sample_output_dir, "original.pcd"), tensor_to_pcd(pc_tensor))
        
        results = {'original': {'points': pc_tensor.cpu().numpy(), 'mask': None}}
        
        # --- 关键改动：为每个生成的缺陷使用随机化的参数 ---
        # 生成4个凸起
        for k in range(4):
            # 在指定的范围内为每次生成随机选择参数，增加多样性
            random_radius_frac = (random.uniform(args.radius_frac[0], args.radius_frac[1]),) * 2
            random_disp_frac = (random.uniform(args.disp_frac[0], args.disp_frac[1]),) * 2
            
            pc_bulge, mask_bulge = augmenter.add_defect(
                pc_tensor.clone(), 
                'bulge',
                radius_frac=random_radius_frac,
                max_disp_frac=random_disp_frac
            )
            results[f'bulge_{k}'] = {'points': pc_bulge.cpu().numpy(), 'mask': mask_bulge.cpu().numpy()}
            o3d.io.write_point_cloud(os.path.join(sample_output_dir, f"bulge_{k}.pcd"), tensor_to_pcd(pc_bulge))

        # 生成4个凹陷
        for k in range(4):
            random_radius_frac = (random.uniform(args.radius_frac[0], args.radius_frac[1]),) * 2
            random_disp_frac = (random.uniform(args.disp_frac[0], args.disp_frac[1]),) * 2

            pc_sink, mask_sink = augmenter.add_defect(
                pc_tensor.clone(), 
                'sink',
                radius_frac=random_radius_frac,
                max_disp_frac=random_disp_frac
            )
            results[f'sink_{k}'] = {'points': pc_sink.cpu().numpy(), 'mask': mask_sink.cpu().numpy()}
            o3d.io.write_point_cloud(os.path.join(sample_output_dir, f"sink_{k}.pcd"), tensor_to_pcd(pc_sink))

        tqdm.write(f"已为 {file_id} 生成并保存所有PCD文件。")

        # --- 使用 Matplotlib 创建可视化 ---
        tqdm.write(f"正在为 {file_id} 创建Matplotlib可视化图...")

        fig = plt.figure(figsize=(18, 18))
        all_points = np.vstack([res['points'] for res in results.values()])
        x_min, x_max = all_points[:, 0].min(), all_points[:, 0].max()
        y_min, y_max = all_points[:, 1].min(), all_points[:, 1].max()
        z_min, z_max = all_points[:, 2].min(), all_points[:, 2].max()
        
        max_range = np.array([x_max-x_min, y_max-y_min, z_max-z_min]).max() / 2.0 * 1.1 # 增加10%的buffer
        mid_x, mid_y, mid_z = (x_min+x_max)/2.0, (y_min+y_max)/2.0, (z_min+z_max)/2.0

        pcd_order = [
            'original', 'bulge_0', 'bulge_1', 'bulge_2', 'bulge_3',
            'sink_0', 'sink_1', 'sink_2', 'sink_3'
        ]

        for idx, key in enumerate(pcd_order):
            ax = fig.add_subplot(3, 3, idx + 1, projection='3d')
            data, points, mask = results[key], results[key]['points'], results[key]['mask']
            
            if mask is None:
                ax.scatter(points[:, 0], points[:, 1], points[:, 2], s=2, c='#6495ED')
                ax.set_title("Original", fontsize=12, pad=10)
            else:
                ax.scatter(points[~mask, 0], points[~mask, 1], points[~mask, 2], s=1.5, c='gray', alpha=0.5)
                color = '#FF4444' if 'sink' in key else '#4444FF'
                ax.scatter(points[mask, 0], points[mask, 1], points[mask, 2], s=4, c=color, alpha=0.9)
                ax.set_title(key.replace('_', ' ').title(), fontsize=12, pad=10)
                
            setup_3d_axis(
                ax,
                [mid_x - max_range, mid_x + max_range],
                [mid_y - max_range, mid_y + max_range],
                [mid_z - max_range, mid_z + max_range]
            )

        fig.suptitle(f'Defect Generation for: {file_id}', fontsize=16)
        plt.tight_layout(rect=[0, 0, 1, 0.96])

        vis_path = os.path.join(sample_output_dir, "visualization_matplotlib.png")
        plt.savefig(vis_path, dpi=200)
        plt.close(fig)
        tqdm.write(f"成功为 {file_id} 保存了Matplotlib可视化图片。")

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="为点云数据集生成并可视化缺陷样本。")
    parser.add_argument('--dataset_path', type=str, default='/home/<USER>/llm/3DAD/data/Real3D-AD-PCD')
    parser.add_argument('--output_dir', type=str, default='./defect_generations_strong', help="保存生成的PCD和图片的目录。")
    parser.add_argument('--categories', type=str, nargs='+', default=['airplane'], help="要处理的物体类别列表。")
    parser.add_argument('--num_points', type=int, default=2048, help="点云采样点数。")
    parser.add_argument('--device', type=str, default='cuda' if torch.cuda.is_available() else 'cpu', help="计算设备。")
    
    # --- 新增：可配置的缺陷强度参数 ---
    parser.add_argument(
        '--radius_frac', 
        type=float, 
        nargs=2, 
        default=[0.10, 0.25], 
        help="缺陷半径范围 (占包围盒比例), e.g., 0.1 0.25"
    )
    parser.add_argument(
        '--disp_frac', 
        type=float, 
        nargs=2, 
        default=[0.04, 0.10], 
        help="最大位移范围 (占包围盒比例), e.g., 0.04 0.1"
    )
    
    args = parser.parse_args()
    main(args)
#!/usr/bin/env python
"""
为数据集中的每个点云生成并可视化缺陷样本。（V4: 不再对点云做任何离线缩放）
"""

import os
import argparse
import random
import numpy as np
import torch
import open3d as o3d
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D  # noqa: F401  # 保留以启用 3D
from tqdm.auto import tqdm

from utils.dataset import Real3DADDataset
from utils.data import DataLoader, custom_collate_fn
from utils.defect_augmentation import RealisticDefectAugmenter
from utils.misc import seed_all


# ----------------------- 工具函数 -----------------------
def tensor_to_pcd(tensor: torch.Tensor) -> o3d.geometry.PointCloud:
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(tensor.cpu().numpy())
    return pcd


def setup_3d_axis(ax, x_lim, y_lim, z_lim, elev=20, azim=-60):
    ax.set_xlim(x_lim)
    ax.set_ylim(y_lim)
    ax.set_zlim(z_lim)
    ax.view_init(elev=elev, azim=azim)
    ax.set_xticks([]), ax.set_yticks([]), ax.set_zticks([])
    for pane in [ax.xaxis.pane, ax.yaxis.pane, ax.zaxis.pane]:
        pane.fill = False
        pane.set_edgecolor('gray')
        pane.set_alpha(0.1)
    ax.set_aspect('auto')


# ----------------------- 主函数 -----------------------
def main(args):
    # 1. 通用初始化
    seed_all(42)
    plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'SimHei', 'Arial Unicode MS']
    plt.rcParams['axes.unicode_minus'] = False
    os.makedirs(args.output_dir, exist_ok=True)

    augmenter = RealisticDefectAugmenter(device=args.device)
    print(f"RealisticDefectAugmenter 初始化完成，使用设备: {args.device}")

    # 2. 构造数据集 —— **绝不缩放**，scale_mode=None
    dset = Real3DADDataset(
        path=args.dataset_path,
        categories=args.categories,
        split='train',
        scale_mode=None,            # <<< 关键：不做 shape_bbox / unit 缩放
        transform=None,
        num_points=None
    )
    loader = DataLoader(
        dset,
        batch_size=1,
        shuffle=False,
        num_workers=4,
        collate_fn=custom_collate_fn
    )
    if len(loader) == 0:
        print("错误: 数据加载器为空，请检查数据集路径和类别设置。")
        return

    print(f"数据加载完成，共 {len(loader)} 个点云。")
    print("缺陷半径范围 (包围盒比例):", args.radius_frac)
    print("最大位移范围   (包围盒比例):", args.disp_frac)

    # 3. 遍历数据集，生成缺陷
    for i, batch in enumerate(tqdm(loader, desc='生成缺陷')):
        # --- 3.1 提取点云 ---
        pc_tensor = batch['pointcloud'].to(args.device)[0]       # (N,3)
        original_filename = batch['filename'][0]
        file_id = os.path.splitext(original_filename)[0]

        # --- 3.2 创建输出目录，保存 original.pcd ---
        sample_out = os.path.join(args.output_dir, file_id)
        os.makedirs(sample_out, exist_ok=True)
        o3d.io.write_point_cloud(
            os.path.join(sample_out, "original.pcd"),
            tensor_to_pcd(pc_tensor)
        )

        # 用于可视化
        results = {'original': {'points': pc_tensor.cpu().numpy(), 'mask': None}}

        # --- 3.3 生成 bulge 缺陷 ---
        for k in range(4):
            rad_frac = (random.uniform(*args.radius_frac),) * 2
            disp_frac = (random.uniform(*args.disp_frac),) * 2
            pc_bulge, mask_bulge = augmenter.add_defect(
                pc_tensor.clone(),
                'bulge',
                radius_frac=rad_frac,
                max_disp_frac=disp_frac
            )
            key = f'bulge_{k}'
            results[key] = {
                'points': pc_bulge.cpu().numpy(),
                'mask':   mask_bulge.cpu().numpy()
            }
            o3d.io.write_point_cloud(
                os.path.join(sample_out, f"{key}.pcd"),
                tensor_to_pcd(pc_bulge)
            )

        # --- 3.4 生成 sink 缺陷 ---
        for k in range(4):
            rad_frac = (random.uniform(*args.radius_frac),) * 2
            disp_frac = (random.uniform(*args.disp_frac),) * 2
            pc_sink, mask_sink = augmenter.add_defect(
                pc_tensor.clone(),
                'sink',
                radius_frac=rad_frac,
                max_disp_frac=disp_frac
            )
            key = f'sink_{k}'
            results[key] = {
                'points': pc_sink.cpu().numpy(),
                'mask':   mask_sink.cpu().numpy()
            }
            o3d.io.write_point_cloud(
                os.path.join(sample_out, f"{key}.pcd"),
                tensor_to_pcd(pc_sink)
            )

        tqdm.write(f"[{file_id}] 已生成并保存 1 original + 8 defects.")

        # --- 3.5 可视化 ---
        fig = plt.figure(figsize=(18, 18))
        all_pts = np.vstack([v['points'] for v in results.values()])
        # 取整体包围盒，留 10% buffer
        xyz_min, xyz_max = all_pts.min(0), all_pts.max(0)
        max_range = (xyz_max - xyz_min).max() / 2 * 1.1
        mid = (xyz_min + xyz_max) / 2

        order = [
            'original',
            'bulge_0', 'bulge_1', 'bulge_2', 'bulge_3',
            'sink_0',  'sink_1',  'sink_2',  'sink_3'
        ]
        for idx, key in enumerate(order):
            ax = fig.add_subplot(3, 3, idx + 1, projection='3d')
            pts = results[key]['points']
            mask = results[key]['mask']
            if mask is None:   # original
                ax.scatter(pts[:, 0], pts[:, 1], pts[:, 2], s=2, c='#6495ED')
                ax.set_title("Original", fontsize=12, pad=10)
            else:              # defects
                ax.scatter(pts[~mask, 0], pts[~mask, 1], pts[~mask, 2],
                           s=1.5, c='gray', alpha=0.5)
                color = '#FF4444' if 'sink' in key else '#4444FF'
                ax.scatter(pts[mask, 0], pts[mask, 1], pts[mask, 2],
                           s=4, c=color, alpha=0.9)
                ax.set_title(key.replace('_', ' ').title(), fontsize=12, pad=10)

            setup_3d_axis(
                ax,
                [mid[0] - max_range, mid[0] + max_range],
                [mid[1] - max_range, mid[1] + max_range],
                [mid[2] - max_range, mid[2] + max_range]
            )

        fig.suptitle(f'Defect Generation for: {file_id}', fontsize=16)
        plt.tight_layout(rect=[0, 0, 1, 0.96])
        plt.savefig(os.path.join(sample_out, "visualization.png"), dpi=200)
        plt.close(fig)
        tqdm.write(f"[{file_id}] 可视化已保存。")


# ----------------------- CLI -----------------------
if __name__ == '__main__':
    parser = argparse.ArgumentParser(
        description="为点云数据集离线生成 bulge/sink 缺陷并保存 .pcd / 可视化。"
    )
    parser.add_argument('--dataset_path', type=str,
                        default='/home/<USER>/llm/3DAD/data/Real3D-AD-PCD')
    parser.add_argument('--output_dir',  type=str,
                        default='./defect_generations_strong')
    parser.add_argument('--categories',  type=str, nargs='+',
                        default=['airplane'])
    parser.add_argument('--num_points', type=int, default=2048)
    parser.add_argument('--device', type=str,
                        default='cuda' if torch.cuda.is_available() else 'cpu')

    # 缺陷强度区间（包围盒比例）
    parser.add_argument('--radius_frac', type=float, nargs=2,
                        default=[0.10, 0.25],
                        help='随机缺陷半径范围 (min max)')
    parser.add_argument('--disp_frac',   type=float, nargs=2,
                        default=[0.04, 0.10],
                        help='随机最大位移范围 (min max)')

    args = parser.parse_args()
    main(args)
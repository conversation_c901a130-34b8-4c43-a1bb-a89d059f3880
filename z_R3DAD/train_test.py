# train_test.py (DDP 启动版)
import argparse
import os
import time
from pathlib import Path
import torch

def main(args):
    world_size = torch.cuda.device_count()
    if world_size < 1:
        print("没有可用的GPU，无法启动DDP训练。")
        return
        
    print(f"检测到 {world_size} 个GPU，将使用 torchrun 启动DDP训练。")

    exp_name = Path(args.config).stem
    time_fix = time.strftime('%Y%m%d-%H%M%S', time.localtime())
    
    # --- 核心修正：--nnodes 必须为 1 ---
    cmd = (
        f"torchrun --standalone --nnodes=1 --nproc_per_node={world_size} "
        f"z_R3DAD/train_r3dad.py --config {args.config} "
        f"--log_root logs_real3d-ad-DDP/{exp_name}_{time_fix}_{args.tag}/ "
        f"--tag {args.tag}"
    )
    
    print(f"\n[Launcher] 正在执行命令:\n{cmd}\n")
    os.system(cmd)


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("config", help="Path to the configuration file (e.g., z_R3DAD/configs/my_real3d_defect_exp.yaml)")
    parser.add_argument('--tag', type=str, default='', help="A custom tag for the log directory")
    args = parser.parse_args()
    main(args)
# z_R3DAD/configs/my_real3d_defect_exp.yaml

# --- 模型参数 ---
model: 'AutoEncoder'
latent_dim: 256
num_steps: 200
beta_1: 0.0001
beta_T: 0.05
sched_mode: 'linear'
residual: True

# --- 数据集与加载器参数 ---
dataset: 'Real3DAD'  # 验证/测试集使用这个类
dataset_path: '/home/<USER>/llm/3DAD/data/Real3D-AD-PCD' # 原始数据集路径
category: 'airplane'
scale_mode: 'shape_bbox'
num_points: 4096 
train_batch_size: 128
val_batch_size: 128
rotate: False
rel: False  # 这里设为False，脚本会根据 use_my_defects 自动覆盖为 True
logging: True
# --- 你的自定义缺陷数据参数 ---
use_my_defects: True
my_defects_path: '/home/<USER>/llm/3DAD/diffusion-point-cloud/z_R3DAD/offline_defects'
# --- 优化器与调度器参数 ---
lr: 0.001
weight_decay: 0
max_grad_norm: 10
sched_start_epoch: 150000 # 150 * 1000
sched_end_epoch: 300000   # 300 * 1000
end_lr: 0.0001 
# --- 训练控制参数 ---
val_freq: 2000
device: 'cuda:0'
max_iters: 40000
seed: 2020


# train_r3dad.py (DDP 最终生产版)

import os
import sys
import argparse
import torch
import torch.utils.tensorboard
from torch.nn.utils import clip_grad_norm_
from tqdm.auto import tqdm
import yaml

# --- DDP 导入 ---
import torch.distributed as dist
from torch.nn.parallel import DistributedDataParallel as DDP
from torch.utils.data.distributed import DistributedSampler
from easydict import EasyDict

# --- 项目模块导入 ---
from utils_z.dataset import *
from utils_z.misc import *
from utils_z.data import *
from utils_z.transform import *
from models_z.autoencoder import *
from evaluation_z import ROC_AP
from models_z.common import get_linear_scheduler

# --- DDP 设置与清理函数 ---
def setup_ddp():
    dist.init_process_group(backend="nccl")
    local_rank = int(os.environ["LOCAL_RANK"])
    torch.cuda.set_device(local_rank)
    return local_rank, dist.get_world_size()

def cleanup_ddp():
    dist.destroy_process_group()

# --- DDP 分布式验证函数 (最终版) ---
def validate_loss(it, model, val_loader, device, args, logger, writer, world_size):
    model.eval()
    
    # 每个进程只存储自己处理的样本结果
    local_results = []
    
    # 每个进程只显示自己的进度条 (rank 0 可见, 其他进程静默)
    iterable = tqdm(val_loader, desc=f'Validate Rank {dist.get_rank()}', disable=(dist.get_rank() != 0))
    for batch in iterable:
        ref = batch['pointcloud'].to(device)
        with torch.no_grad():
            code = model.module.encode(ref)
            recons = model.module.decode(code, ref.size(1), flexibility=args.get('flexibility', 0.0))
        
        # 将一个批次的结果逐一样本存入列表，包含反归一化所需信息
        for i in range(ref.size(0)):
            local_results.append({
                'ref': ref[i].cpu(),
                'recons': recons[i].cpu(),
                'label': batch['label'][i].cpu(),
                'mask': batch['mask'][i].cpu(),
                'scale': batch['scale'][i].cpu(),
                'shift': batch['shift'][i].cpu()
            })
            
    # 等待所有进程完成各自的验证循环
    dist.barrier()

    # 使用 all_gather_object 收集来自所有进程的 local_results 列表
    # 这个方法可以正确处理每个进程样本数不同的情况
    gathered_results_list = [None] * world_size
    dist.all_gather_object(gathered_results_list, local_results)

    score = 0.0
    # 只在主进程 (rank 0) 上进行汇总计算和日志记录
    if dist.get_rank() == 0:
        # 将收集到的结果列表展开成一个大的列表
        flat_results = [item for sublist in gathered_results_list for item in sublist]
        
        logger.info(f"全局验证样本总数: {len(flat_results)}")
        
        all_refs, all_recons, all_labels, all_masks = [], [], [], []
        for res in flat_results:
            # --- 关键步骤: 进行反归一化 ---
            ref_denorm = res['ref'] * res['scale'] + res['shift']
            recons_denorm = res['recons'] * res['scale'] + res['shift']
            
            all_refs.append(ref_denorm)
            all_recons.append(recons_denorm)
            all_labels.append(res['label'])
            all_masks.append(res['mask'])
        
        # 将列表拼接成最终的张量
        all_refs = torch.stack(all_refs, dim=0)
        all_recons = torch.stack(all_recons, dim=0)
        all_labels = torch.stack(all_labels, dim=0)
        all_masks = torch.stack(all_masks, dim=0)

        all_refs = all_refs.to(device)
        all_recons = all_recons.to(device)
        all_labels = all_labels.to(device)
        all_masks = all_masks.to(device)
 
        # 在反归一化后的原始坐标空间中计算指标
        metrics = ROC_AP(all_refs, all_recons, all_labels, all_masks)
        
        roc_i = metrics['ROC_i']
        score = roc_i # 使用 Image-level AUROC 作为保存模型的依据
        
        logger.info(f"[Val] Iter {it} | Global ROC_i {roc_i:.6f}")
        writer.add_scalar('val/ROC_i', roc_i, it)
        # 您也可以记录其他指标
        # roc_p = metrics['ROC_p'].item()
        # writer.add_scalar('val/ROC_p', roc_p, it)

    # 等待主进程完成指标计算和日志记录
    dist.barrier()
    return score

# --- 主函数 ---
def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--config', type=str, required=True, help='Path to the config file')
    parser.add_argument('--log_root', type=str, required=True, help='Root directory for logging')
    parser.add_argument('--tag', type=str, default=None, help='A custom tag for the log directory')
    base_args = parser.parse_args()

    args = EasyDict()
    with open(base_args.config, 'r') as f:
        args.update(yaml.safe_load(f))
    args.log_root = base_args.log_root
    args.tag = base_args.tag
    
    local_rank, world_size = setup_ddp()
    device = torch.device(f"cuda:{local_rank}")
    seed_all(args.seed + local_rank)

    if local_rank == 0:
        log_dir = get_new_log_dir(args.log_root, prefix=args.category + '_')
        logger = get_logger('train', log_dir)
        writer = torch.utils.tensorboard.SummaryWriter(log_dir)
        ckpt_mgr = CheckpointManager(log_dir)
        logger.info("DDP training initialized.")
        logger.info(f"Running with {world_size} GPUs.")
        logger.info(args)
    else:
        logger = writer = ckpt_mgr = BlackHole()

    logger.info('Loading datasets...')
    train_transforms = []
    if args.get('rotate', False):
        train_transforms.append(RandomRotate(180, ['pointcloud', 'pointcloud_raw']))
    
    if args.use_my_defects:
        train_dset = OfflineDefectDataset(path=args.my_defects_path, cates=[args.category], transforms=train_transforms)
    else:
        raise NotImplementedError("当前只支持 use_my_defects 离线模式")

    val_dset = Real3DAD(path=args.dataset_path, cates=[args.category], split='test', 
                        scale_mode=args.scale_mode, num_points=args.num_points)

    train_sampler = DistributedSampler(train_dset, num_replicas=world_size, rank=local_rank, shuffle=True)
    val_sampler = DistributedSampler(val_dset, num_replicas=world_size, rank=local_rank, shuffle=False)
    
    train_loader = DataLoader(train_dset, batch_size=args.train_batch_size // world_size, 
                              num_workers=4, pin_memory=True, sampler=train_sampler)
    val_loader = DataLoader(val_dset, batch_size=args.val_batch_size // world_size, 
                            num_workers=4, pin_memory=True, sampler=val_sampler)
    train_iter = get_data_iterator(train_loader)

    logger.info('Building model...')
    model = getattr(sys.modules[__name__], args.model)(args).to(device)
    model = DDP(model, device_ids=[local_rank])
    
    optimizer = torch.optim.Adam(model.parameters(), lr=args.lr, weight_decay=args.get('weight_decay', 0))
    scheduler = get_linear_scheduler(optimizer, start_epoch=args.sched_start_epoch, 
                                     end_epoch=args.sched_end_epoch, start_lr=args.lr, end_lr=args.end_lr)

    logger.info('Start training...')
    try:
        for it in range(1, args.max_iters + 1):
            train_sampler.set_epoch(it)
            
            batch = next(train_iter)
            x = batch['pointcloud'].to(device)
            x_raw = batch['pointcloud_raw'].to(device)
            
            model.train()
            optimizer.zero_grad()
            
            loss = model.module.get_loss(x, x_raw)
            
            loss.backward()
            orig_grad_norm = clip_grad_norm_(model.parameters(), args.max_grad_norm)
            optimizer.step()
            scheduler.step()
            
            if local_rank == 0 and (it % 50 == 0 or it == 1):
                logger.info('[Train] Iter %d | Loss %.6f | Grad %.4f' % (it, loss.item(), orig_grad_norm))
                writer.add_scalar('train/loss', loss, it)
                writer.add_scalar('train/lr', optimizer.param_groups[0]['lr'], it)

            if it % args.val_freq == 0 or it == args.max_iters:
                score = validate_loss(it, model, val_loader, device, args, logger, writer, world_size)
                if local_rank == 0:
                    logger.info(f"Saving checkpoint at iteration {it} with score {score:.6f}")
                    opt_states = {'optimizer': optimizer.state_dict(), 'scheduler': scheduler.state_dict()}
                    ckpt_mgr.save(model, args, score, opt_states, it)
                dist.barrier()
                
    except KeyboardInterrupt:
        logger.info('Terminating...')
    finally:
        cleanup_ddp()

if __name__ == '__main__':
    main()
from __future__ import annotations
import os
import random
from copy import copy
import torch
from torch.utils.data import Dataset
import numpy as np
import open3d as o3d
import glob
from typing import Tuple
all_shapenetad_cates = ['ashtray0', 'bag0', 'bottle0', 'bottle1', 'bottle3', 'bowl0', 'bowl1', 'bowl2', 'bowl3', 'bowl4', 'bowl5', 'bucket0', 'bucket1', 'cap0', 'cap3', 'cap4', 'cap5', 'cup0', 'cup1', 'eraser0', 'headset0', 'headset1', 'helmet0', 'helmet1', 'helmet2', 'helmet3', 'jar0', 'microphone0', 'shelf0', 'tap0', 'tap1', 'vase0', 'vase1', 'vase2', 'vase3', 'vase4', 'vase5', 'vase7', 'vase8', 'vase9']
# --- 辅助工具 ---
def farthest_point_sampling(points, npoint):
    try:
        import torch_cluster
    except ImportError:
        raise ImportError("请安装 torch_cluster: pip install torch_cluster")
    N = points.size(0)
    if N == npoint: return points
    if N < npoint:
        repeats = (npoint + N - 1) // N
        indices = torch.arange(N, device=points.device).repeat(repeats)[:npoint]
        return points[indices]
    ratio = npoint / N
    indices = torch_cluster.fps(points.float(), ratio=ratio)
    if len(indices) < npoint:
        mask = torch.ones(N, dtype=torch.bool, device=points.device)
        mask[indices] = False
        num_needed = npoint - len(indices)
        available_indices = torch.where(mask)[0]
        if len(available_indices) >= num_needed:
            extra_indices = available_indices[torch.randperm(len(available_indices), device=points.device)[:num_needed]]
        else:
            extra_indices = indices[torch.randint(0, len(indices), (num_needed,), device=points.device)]
        indices = torch.cat([indices, extra_indices], dim=0)
    elif len(indices) > npoint:
        indices = indices[:npoint]
    return points[indices]

def local_frame(pts: torch.Tensor):
    origin = pts.mean(dim=0)
    X = pts - origin
    cov = X.T @ X / max(1, X.shape[0] - 1)
    _, vecs = torch.linalg.eigh(cov)
    axes = torch.flip(vecs, dims=[1])
    if torch.linalg.det(axes) < 0: axes[:, -1].mul_(-1)
    return origin, axes

class RealisticDefectAugmenter:
    def __init__(self, device='cuda' if torch.cuda.is_available() else 'cpu'):
        self.device = torch.device(device)
        # 可以加一句打印，方便确认
        print(f"[Augmenter] Initialized on device: {self.device}")
    
    @torch.no_grad()
    def add_defect(self, points: torch.Tensor, defect_type: str, radius_frac: Tuple[float, float],
                   max_disp_frac: Tuple[float, float], core_patch_size: int = 45):
        pts = points.to(self.device)
        N = pts.size(0)
        bb_min, _ = pts.min(0)
        bb_max, _ = pts.max(0)
        diag = torch.norm(bb_max - bb_min)
        radius = diag * random.uniform(*radius_frac)
        max_disp = diag * random.uniform(*max_disp_frac)
        direction = 1.0 if defect_type == 'bulge' else -1.0
        c_idx = random.randint(0, N - 1)
        dists = torch.norm(pts - pts[c_idx], dim=1)
        k = min(core_patch_size, N)
        patch = torch.topk(dists, k=k, largest=False).indices
        core = pts[patch]
        origin, axes = local_frame(core)
        xyz_local = (pts - origin) @ axes
        uv, w = xyz_local[:, :2], xyz_local[:, 2:3]
        U, W = uv[patch], w[patch]
        A = torch.stack([U[:, 0]**2, U[:, 1]**2, U[:, 0] * U[:, 1]], dim=1)
        try:
            coeff = torch.linalg.lstsq(A, W).solution
        except AttributeError:
            coeff, _ = torch.lstsq(W, A)
        a, b, c = coeff.squeeze()
        r2d = torch.norm(uv, dim=1)
        mask = r2d < radius
        if mask.sum() == 0:
            return pts.cpu(), torch.zeros(N, dtype=torch.bool)
        fall = (1 - (r2d[mask] / radius) ** 2).pow(2).unsqueeze(1)
        u, v = uv[mask, 0:1], uv[mask, 1:2]
        h0 = a * u ** 2 + b * v ** 2 + c * u * v
        h1 = h0 + direction * max_disp * fall
        delta = h1 - w[mask]
        normal = axes[:, 2] / torch.norm(axes[:, 2])
        disp = delta * normal
        pts_new = pts.clone()
        pts_new[mask] += disp
        return pts_new.cpu(), mask.cpu()

# --- 主要的数据集类 ---

class Real3DAD(Dataset):
    """用于加载 Real3D-AD 原始 .pcd 数据集 (用于验证/测试)"""
    def __init__(self, path, cates, split, scale_mode='shape_bbox', num_points=2048, transforms=None, **kwargs):
        super().__init__()
        self.path, self.cates, self.split = path, cates, split
        self.scale_mode, self.num_points = scale_mode, num_points
        self.pointclouds, self.transforms = [], transforms if transforms is not None else []
        self._load()

    def _load(self):
        for cate in self.cates:
            cate_dir = os.path.join(self.path, cate, self.split)
            if not os.path.isdir(cate_dir): continue
            for fname in os.listdir(cate_dir):
                if not fname.endswith('.pcd'): continue
                pc_np, mask, label = None, None, 0
                if self.split == 'train' or 'good' in fname:
                    pcd = o3d.io.read_point_cloud(os.path.join(cate_dir, fname))
                    pc_np = np.asarray(pcd.points, dtype=np.float32)
                    mask, label = np.zeros(len(pc_np), dtype=np.bool_), 0
                else:
                    label = 1
                    mask_path = os.path.join(self.path, cate, 'gt', fname.replace('.pcd', '.txt'))
                    if os.path.exists(mask_path):
                        pointcloud_and_mask = np.loadtxt(mask_path, dtype=np.float32)
                        pc_np = pointcloud_and_mask[:, :3]
                        mask = pointcloud_and_mask[:, 3].astype(np.bool_)
                    else:
                        pcd = o3d.io.read_point_cloud(os.path.join(cate_dir, fname))
                        pc_np = np.asarray(pcd.points, dtype=np.float32)
                        mask = np.zeros(len(pc_np), dtype=np.bool_)
                if pc_np is not None:
                    self.pointclouds.append({
                        'raw_points': torch.from_numpy(pc_np), 'category': cate, 'filename': fname,
                        'label': torch.tensor(label, dtype=torch.long), 'mask': torch.from_numpy(mask)
                    })

    def __len__(self): return len(self.pointclouds)

    def __getitem__(self, idx):
        data = self.pointclouds[idx]
        pc, mask = data['raw_points'].clone(), data['mask'].clone()
        shift, scale_val = torch.zeros(1, 3), torch.ones(1, 1)
        if self.scale_mode == 'shape_bbox':
            pc_max, _ = pc.max(0, keepdim=True)
            pc_min, _ = pc.min(0, keepdim=True)
            shift = (pc_min + pc_max) / 2
            scale_val = ((pc_max - pc_min).max() / 2).clamp(min=1e-8)
            pc = (pc - shift) / scale_val
        if self.num_points is not None:
            replace = len(pc) < self.num_points
            choice = np.random.choice(len(pc), self.num_points, replace=replace)
            pc, mask = pc[choice], mask[choice]
        item = {
            'pointcloud': pc.contiguous(), 'category': data['category'], 'id': data['filename'],
            'shift': shift.contiguous(), 'scale': scale_val.expand(1, 3).contiguous(),
            'label': data['label'], 'mask': mask.contiguous()
        }
        for transform in self.transforms:
            item = transform(item)
        return item

class OfflineDefectDataset(Dataset):
    """用于加载预计算的离线 .pt 缺陷数据对 (用于训练)"""
    def __init__(self, path, cates, transforms=None, **kwargs):
        super().__init__()
        self.transforms = transforms if transforms else []
        category = cates[0]
        data_dir = os.path.join(path, category)
        self.data_files = sorted(list(glob.glob(os.path.join(data_dir, '*.pt'))))
        if not self.data_files:
            raise FileNotFoundError(f"在 {data_dir} 中没有找到任何 .pt 文件。")
        print(f"从离线目录加载了 {len(self.data_files)} 个预计算样本。")

    def __len__(self): return len(self.data_files)

    def __getitem__(self, idx):
        data = torch.load(self.data_files[idx])
        for transform in self.transforms:
            data = transform(data)
        return data
import yaml
from easydict import EasyDict

def merge_new_config(config, new_config):
    if '_BASE_CONFIG_' in new_config:
        with open(new_config['_BASE_CONFIG_'], 'r') as f:
            try:
                yaml_config = yaml.safe_load(f, Loader=yaml.FullLoader)
            except:
                yaml_config = yaml.safe_load(f)
        config.update(EasyDict(yaml_config))

    for key, val in new_config.items():
        if '_BASE_CONFIG_' in key:
            continue
        if not isinstance(val, dict):
            config[key] = val
            continue
        if key not in config:
            config[key] = EasyDict()
        merge_new_config(config[key], val)

    return config

def cfg_from_yaml_file(cfg_file, config):
    with open(cfg_file, 'r') as f:
        try:
            new_config = yaml.safe_load(f, Lo<PERSON>=yaml.FullLoader)
        except:
            new_config = yaml.safe_load(f)

        merge_new_config(config=config, new_config=new_config)

    return config

def cmd_from_config(cfg_file):
    cfg = EasyDict()
    cfg_from_yaml_file(cfg_file, cfg)
    cmd = ""
    for key, value in cfg.items():
        if isinstance(value, bool):
            if value:
                cmd += f" --{key}"
            # 如果值为 False，则什么都不添加
        elif isinstance(value, list):
            # --- 新增的逻辑：如果值是列表 ---
            # 将列表中的每个元素转换成字符串，并用空格连接
            list_values = " ".join(map(str, value))
            cmd += f" --{key} {list_values}"
        else:
            # 对于所有其他类型的值（字符串，数字等），正常添加键和值
            cmd += f" --{key} {value}"
    return cmd
# precompute_defects_offline.py (多GPU + 内部批处理 终极加速版)

import os
import torch
import random
from tqdm.auto import tqdm
from pathlib import Path
import torch.multiprocessing as mp
import time

from utils_z.dataset import Real3DAD, farthest_point_sampling, RealisticDefectAugmenter

# --- 配置参数 ---
CONFIG = {
    "raw_data_path": "/home/<USER>/llm/3DAD/data/Real3D-AD-PCD",
    "output_dir": "./offline_defects",
    "category": "airplane",
    "num_points": 4096,
    "total_anomalies": 40000,
    "aug_radius_frac": (0.10, 0.25),
    "aug_disp_frac": (0.04, 0.10),
    "gpu_device_base": "cuda" # 使用 cuda:0, cuda:1 ...
}

def worker(rank, world_size, config):
    """
    每个GPU进程执行的工作函数。
    """
    device = torch.device(f"{config['gpu_device_base']}:{rank}")
    
    # 计算当前进程的任务范围
    num_samples_per_gpu = config["total_anomalies"] // world_size
    start_index = rank * num_samples_per_gpu
    end_index = start_index + num_samples_per_gpu
    if rank == world_size - 1:
        end_index = config["total_anomalies"]

    # 每个进程独立加载模板数据
    if rank == 0:
        print("正在加载原始正常样本作为模板...")
    raw_data_source = Real3DAD(
        path=config["raw_data_path"], cates=[config["category"]], split='train',
        num_points=None, scale_mode=None
    )
    
    # --- 关键修正：从字典中提取 'raw_points' 张量再移动到GPU ---
    templates_gpu = [data['raw_points'].to(device) for data in raw_data_source.pointclouds]
    
    if rank == 0:
        print(f"加载并移动了 {len(templates_gpu)} 个模板到各个GPU。")

    # 在各自的GPU上初始化缺陷生成器
    augmenter = RealisticDefectAugmenter(device=device)

    # 循环生成和保存分配给自己的数据
    output_path = Path(config["output_dir"]) / config["category"]
    for i in tqdm(range(start_index, end_index), position=rank, desc=f"GPU {rank}"):
        raw_norm_points_gpu = random.choice(templates_gpu).clone()
        
        pc_max, _ = raw_norm_points_gpu.max(0, keepdim=True)
        pc_min, _ = raw_norm_points_gpu.min(0, keepdim=True)
        shift = (pc_min + pc_max) / 2
        scale = ((pc_max - pc_min).max() / 2).clamp(min=1e-8)
        norm_points_scaled_gpu = (raw_norm_points_gpu - shift) / scale
        pointcloud_raw_gpu = farthest_point_sampling(norm_points_scaled_gpu, config["num_points"])
        
        defect_type = random.choice(['bulge', 'sink'])
        pointcloud_anom_cpu, _ = augmenter.add_defect(
            pointcloud_raw_gpu,
            defect_type=defect_type,
            radius_frac=config["aug_radius_frac"],
            max_disp_frac=config["aug_disp_frac"]
        )
        
        data_pair = {
            'pointcloud': pointcloud_anom_cpu.contiguous(),
            'pointcloud_raw': pointcloud_raw_gpu.cpu().contiguous(),
            'cate': config["category"],
            'id': f'offline_aug_{i}'
        }
        
        torch.save(data_pair, output_path / f"{i:06d}.pt")

def main():
    world_size = torch.cuda.device_count()
    if world_size < 1:
        print("没有可用的GPU，请检查环境。")
        return
        
    print(f"发现 {world_size} 个GPU，将启动 {world_size} 个并行进程。")
    
    output_path = Path(CONFIG["output_dir"]) / CONFIG["category"]
    output_path.mkdir(parents=True, exist_ok=True)
    print(f"数据将保存到: {output_path}")

    mp.spawn(worker,
             args=(world_size, CONFIG),
             nprocs=world_size,
             join=True)
    
    print("\n所有GPU进程已完成数据生成！")

if __name__ == "__main__":
    mp.set_start_method("spawn")
    main()
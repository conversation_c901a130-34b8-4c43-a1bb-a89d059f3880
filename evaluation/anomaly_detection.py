import torch
import numpy as np
from sklearn.metrics import roc_auc_score, precision_recall_curve, average_precision_score
from .evaluation_metrics import dist<PERSON><PERSON><PERSON>

def compute_anomaly_score(real_pc, gen_pc):
    dl, dr = distChamfer(real_pc.unsqueeze(0), gen_pc.unsqueeze(0))
    cd_dist = (dl.mean() + dr.mean()) / 2
    return cd_dist

def evaluate_anomaly_detection(model, test_loader, device):
    model.eval()
    scores = []
    labels = []
    
    with torch.no_grad():
        for batch in test_loader:
            # 移动数据到设备
            batch = {k: v.to(device) if isinstance(v, torch.Tensor) else v 
                    for k, v in batch.items()}
            
            # 生成异常样本
            gen_pc = model.sample(
                num_points=batch['pos'].shape[1],
                context=batch['pos'],
                anomaly=True,
                anomaly_strength=0.1
            )
            
            # 计算异常分数
            score = compute_anomaly_score(batch['pos'], gen_pc)
            scores.append(score.cpu())
            labels.append(batch['label'])
    
    # 转换为numpy数组
    scores = torch.cat(scores).numpy()
    labels = torch.cat(labels).numpy()
    
    # 计算评估指标
    auroc = roc_auc_score(labels, scores)
    ap = average_precision_score(labels, scores)
    
    # 计算最佳F1分数
    precisions, recalls, thresholds = precision_recall_curve(labels, scores)
    f1_scores = 2 * precisions * recalls / (precisions + recalls + 1e-8)
    best_f1 = np.max(f1_scores)
    
    results = {
        'AUROC': auroc,
        'AP': ap,
        'Best-F1': best_f1
    }
    
    return results
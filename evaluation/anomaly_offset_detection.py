import torch
import numpy as np
from sklearn.metrics import roc_auc_score, average_precision_score
from tqdm.auto import tqdm

from utils.data import custom_collate_fn, DataLoader
from utils.dataset import Real3DADDataset
from models.autoencoder import AutoEncoder, reparameterize_gaussian


def point_offsets(pc_in, pc_rec):
    """Compute per-point nearest-neighbor distance from pc_in to pc_rec.
    pc_in:  (N,3)
    pc_rec: (M,3)
    returns dist (N,)
    """
    # torch.cdist is (1,N,3) vs (1,M,3)
    d = torch.cdist(pc_in.unsqueeze(0), pc_rec.unsqueeze(0))  # (1,N,M)
    dist, _ = d.min(dim=2)  # (1,N)
    return dist.squeeze(0)  # (N,)


def evaluate(model, dset, batch_size=4, device='cuda', agg="mean"):
    loader = DataLoader(dset, batch_size=batch_size, shuffle=False, num_workers=4, collate_fn=custom_collate_fn)
    model.eval()
    all_scores = []
    all_labels = []
    with torch.no_grad():
        for batch in tqdm(loader, desc='OffsetDet'):
            pcs = batch['pointcloud'].to(device)  # (B,N,3)
            labels = torch.tensor(batch['label']).to(device) if 'label' in batch else None

            # Context: assume "normal" (label idx 0)
            B, N, _ = pcs.shape
            latent = model.encode(pcs, return_log_variance=False)
            normal_idx = torch.zeros(B, dtype=torch.long, device=device)
            label_vec = model.label_emb(normal_idx)
            context = torch.cat([latent, label_vec], dim=1)

            rec = model.diffusion_decoder.sample(num_points=N, context=context, batch_size=B, device=device, point_dim=3, ret_traj=False)
            rec_xyz = rec.to(device)

            # Compute scores per sample
            for b in range(B):
                dist = point_offsets(pcs[b], rec_xyz[b])
                score = dist.mean() if agg=="mean" else dist.max()
                all_scores.append(score.cpu())
                if labels is not None:
                    all_labels.append(labels[b].cpu())
    scores_np = torch.stack(all_scores).numpy()
    if all_labels:
        labels_np = torch.stack(all_labels).numpy()
        auroc = roc_auc_score(labels_np, scores_np)
        ap = average_precision_score(labels_np, scores_np)
        return {'AUROC': auroc, 'AP': ap}
    return scores_np 
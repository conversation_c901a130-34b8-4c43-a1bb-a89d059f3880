#
# 文件名: utils/my_precomputed_dataset.py
#

import os
import glob
import torch
import numpy as np
import open3d as o3d
from torch.utils.data import Dataset
from copy import copy

class MyPrecomputedDefectDataset(Dataset):
    """
    加载由脚本预先生成的缺陷样本。
    为 R3D-AD 的训练流程提供 (异常样本, 正常样本) 对。
    """
    def __init__(self, path, cates, split, scale_mode, num_points, transforms=None, **kwargs):
        super().__init__()
        # 兼容 R3D-AD 的 dataset 初始化参数
        assert split == 'train', "这个Dataset只用于从预计算目录加载训练数据"

        # path 参数现在指向预计算缺陷的根目录 (e.g., './defect_generations_strong')
        self.root_dir = path
        self.num_points = num_points
        self.transforms = transforms
        self.samples = []
        
        print(f"--- 初始化 MyPrecomputedDefectDataset ---")
        print(f"扫描目录: {self.root_dir}")
        print(f"处理类别: {cates}")

        # 扫描缺陷目录并构建样本列表
        for category in cates:
            category_path = os.path.join(self.root_dir, category)
            if not os.path.isdir(category_path):
                print(f"警告: 在 {self.root_dir} 中找不到类别目录 {category}，跳过。")
                continue

            template_dirs = [d for d in glob.glob(os.path.join(category_path, '*')) if os.path.isdir(d)]
            
            for dir_path in template_dirs:
                original_pcd_path = os.path.join(dir_path, 'original.pcd')
                if not os.path.exists(original_pcd_path):
                    continue

                defect_files = glob.glob(os.path.join(dir_path, '*.pcd'))
                for defect_path in defect_files:
                    if os.path.basename(defect_path) == 'original.pcd':
                        continue
                    
                    # 每个样本包含: (异常pcd路径, 对应的原始pcd路径)
                    self.samples.append({
                        'anom_path': defect_path,
                        'norm_path': original_pcd_path,
                        'category': category
                    })

        if not self.samples:
            raise FileNotFoundError(f"在目录 {self.root_dir} 中没有找到任何有效的缺陷样本。")
            
        print(f"扫描完成，共找到 {len(self.samples)} 个预计算的缺陷样本对。")

    def __len__(self):
        return len(self.samples)

    def __getitem__(self, idx):
        sample_info = self.samples[idx]

        # --- 加载点云 ---
        try:
            anom_pcd = o3d.io.read_point_cloud(sample_info['anom_path'])
            norm_pcd = o3d.io.read_point_cloud(sample_info['norm_path'])
            
            anom_points = np.asarray(anom_pcd.points, dtype=np.float32)
            norm_points = np.asarray(norm_pcd.points, dtype=np.float32)

            # --- 采样到固定的点数 ---
            if len(anom_points) > self.num_points:
                choice = np.random.choice(len(anom_points), self.num_points, replace=False)
                anom_points = anom_points[choice]
            
            if len(norm_points) > self.num_points:
                choice = np.random.choice(len(norm_points), self.num_points, replace=False)
                norm_points = norm_points[choice]

            data = {
                'pointcloud': torch.from_numpy(anom_points),      # R3D-AD 默认使用 'pointcloud' 作为输入
                'pointcloud_raw': torch.from_numpy(norm_points),  # 'pointcloud_raw' 作为重建目标
                'cate': sample_info['category'],
            }

            # 应用 R3D-AD 的数据增强
            if self.transforms:
                for transform in self.transforms:
                    data = transform(data)
            
            return data
            
        except Exception as e:
            print(f"错误: 加载或处理文件失败 {sample_info['anom_path']} 或 {sample_info['norm_path']}: {e}")
            return None # DataLoader 会自动跳过 None
import os, glob, torch
from torch.utils.data import Dataset

class ALDACacheDataset(Dataset):
    """Dataset for pre-computed ALDA anomaly samples.

    每个 .pt 文件应包含：
        'pc_anom': (N,3) float32  – 缺陷点云
        'latent' : (latent_dim,)  – 预编码潜向量
        'onehot' 或 'label_idx':  – 缺陷类型 (可选)
    """

    def __init__(self, cache_dir: str):
        super().__init__()
        if not os.path.isdir(cache_dir):
            raise FileNotFoundError(f"ALDA cache dir not found: {cache_dir}")
        # 支持子目录结构，递归收集所有 .pt
        self.file_paths = sorted(glob.glob(os.path.join(cache_dir, '**', '*.pt'), recursive=True))
        if len(self.file_paths) == 0:
            raise RuntimeError(f"No .pt files found under {cache_dir}")

    def __len__(self):
        return len(self.file_paths)

    def __getitem__(self, idx):
        fpath = self.file_paths[idx]
        data = torch.load(fpath, map_location='cpu')  # 训练时再搬到 device

        pc = data.get('pc_anom') or data.get('pointcloud')
        if pc is None:
            raise KeyError(f"pc_anom not found in {fpath}")
        latent = data.get('latent')
        if latent is None:
            raise KeyError(f"latent not found in {fpath}")

        # 解析缺陷类别
        if 'label_idx' in data:
            label_idx = torch.as_tensor(int(data['label_idx']), dtype=torch.long)
        elif 'onehot' in data:
            label_idx = torch.argmax(data['onehot']).long()
        else:
            # 默认 normal
            label_idx = torch.tensor(0, dtype=torch.long)

        return {
            'pointcloud': pc,   # (N,3)
            'latent': latent,   # (D,)
            'label_idx': label_idx  # ()
        } 
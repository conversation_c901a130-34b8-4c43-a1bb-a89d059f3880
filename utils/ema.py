import torch
import torch.nn as nn
from typing import Dict, Any
import copy

class EMA:
    """
    指数移动平均（Exponential Moving Average）工具类
    用于模型权重的平滑更新，减少训练过程中的权重抖动
    
    特别适用于扩散模型等对权重敏感的生成模型
    """
    
    def __init__(self, model: nn.Module, decay: float = 0.9999, device: str = None):
        """
        初始化EMA
        
        Args:
            model: 要应用EMA的模型
            decay: 衰减率，通常设为0.9999或0.999
            device: 设备，如果为None则自动检测
        """
        self.decay = decay
        self.device = device or next(model.parameters()).device
        
        # 保存原始模型引用
        self.model = model
        
        # 创建EMA权重的副本
        self.shadow = {}
        self.backup = {}
        
        # 初始化shadow参数
        self._initialize_shadow()
        
        # 更新计数器，用于bias correction
        self.num_updates = 0
    
    def _initialize_shadow(self):
        """初始化shadow参数为模型当前参数的副本"""
        for name, param in self.model.named_parameters():
            if param.requires_grad:
                self.shadow[name] = param.data.clone().to(self.device)
    
    def update(self, model: nn.Module = None):
        """
        更新EMA权重
        
        Args:
            model: 要更新的模型，如果为None则使用初始化时的模型
        """
        if model is None:
            model = self.model
            
        self.num_updates += 1
        
        # Bias correction for early training steps
        # 在训练初期，EMA权重可能偏向初始值，需要校正
        decay = min(self.decay, (1 + self.num_updates) / (10 + self.num_updates))
        
        with torch.no_grad():
            for name, param in model.named_parameters():
                if param.requires_grad and name in self.shadow:
                    # EMA更新: shadow = decay * shadow + (1 - decay) * current
                    self.shadow[name].mul_(decay).add_(param.data, alpha=1.0 - decay)
    
    def apply_shadow(self):
        """应用EMA权重到模型（通常在验证时使用）"""
        self._backup_current_params()
        self._load_shadow_params()
    
    def restore(self):
        """恢复原始训练权重"""
        self._restore_backup_params()
    
    def _backup_current_params(self):
        """备份当前模型参数"""
        for name, param in self.model.named_parameters():
            if param.requires_grad:
                self.backup[name] = param.data.clone()
    
    def _load_shadow_params(self):
        """将EMA权重加载到模型"""
        for name, param in self.model.named_parameters():
            if param.requires_grad and name in self.shadow:
                param.data.copy_(self.shadow[name])
    
    def _restore_backup_params(self):
        """恢复备份的参数"""
        for name, param in self.model.named_parameters():
            if param.requires_grad and name in self.backup:
                param.data.copy_(self.backup[name])
    
    def state_dict(self) -> Dict[str, Any]:
        """获取EMA状态字典，用于保存checkpoint"""
        return {
            'decay': self.decay,
            'num_updates': self.num_updates,
            'shadow': self.shadow
        }
    
    def load_state_dict(self, state_dict: Dict[str, Any]):
        """从状态字典加载EMA状态"""
        self.decay = state_dict['decay']
        self.num_updates = state_dict['num_updates']
        self.shadow = state_dict['shadow']
        
        # 确保shadow参数在正确的设备上
        for name in self.shadow:
            self.shadow[name] = self.shadow[name].to(self.device)
    
    def copy_to(self, target_model: nn.Module):
        """将EMA权重复制到目标模型"""
        with torch.no_grad():
            for name, param in target_model.named_parameters():
                if param.requires_grad and name in self.shadow:
                    param.data.copy_(self.shadow[name])


class EMAWrapper:
    """
    EMA包装器，简化在训练循环中的使用
    
    用法示例:
    ```python
    ema_wrapper = EMAWrapper(model, decay=0.9999)
    
    # 训练循环中
    loss.backward()
    optimizer.step()
    ema_wrapper.update()
    
    # 验证时
    with ema_wrapper.average_parameters():
        validate_loss()
    ```
    """
    
    def __init__(self, model: nn.Module, decay: float = 0.9999, device: str = None):
        self.ema = EMA(model, decay, device)
        self.model = model
    
    def update(self):
        """更新EMA权重"""
        self.ema.update()
    
    def average_parameters(self):
        """上下文管理器，自动应用和恢复EMA权重"""
        return EMAContext(self.ema)
    
    def state_dict(self):
        """获取状态字典"""
        return self.ema.state_dict()
    
    def load_state_dict(self, state_dict):
        """加载状态字典"""
        self.ema.load_state_dict(state_dict)


class EMAContext:
    """EMA上下文管理器"""
    
    def __init__(self, ema: EMA):
        self.ema = ema
    
    def __enter__(self):
        self.ema.apply_shadow()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.ema.restore() 
import os
import torch
import numpy as np
import random
import time
import logging
import logging.handlers
import shutil

THOUSAND = 1000
MILLION = 1000000


class BlackHole(object):
    def __setattr__(self, name, value):
        pass
    def __call__(self, *args, **kwargs):
        return self
    def __getattr__(self, name):
        return self


class CheckpointManager(object):
    
    def __init__(self, save_dir, max_to_keep=5, keep_checkpoint_every_n_hours=None):
        """
        Args:
            save_dir: 保存检查点的目录
            max_to_keep: 最多保存的检查点数量
            keep_checkpoint_every_n_hours: 每n小时至少保存一个检查点，不管指标如何
        """
        self.save_dir = save_dir
        self.max_to_keep = max_to_keep
        self.keep_checkpoint_every_n_hours = keep_checkpoint_every_n_hours
        
        # 确保目录存在
        os.makedirs(save_dir, exist_ok=True)
        
        # 检查点数据结构：{步骤：(指标, 时间戳, 文件路径)}
        self.checkpoints = {}
    
    def save(self, model, args, metric_value, others=None, step=None):
        """保存模型检查点
        
        Args:
            model: 要保存的模型
            args: 模型参数
            metric_value: 用于确定最佳模型的指标值（对于JSD等指标，值越小越好）
            others: 其他要保存的内容，如优化器状态
            step: 当前步骤/迭代次数
        """
        if not os.path.exists(self.save_dir):
            os.makedirs(self.save_dir)
        
        if step is None:
            step = len(self.checkpoints) + 1
        
        # 创建新检查点
        checkpoint_dict = {
            'args': args,
            'state_dict': model.state_dict(),
            'metric': metric_value,
            'step': step
        }
        
        if others is not None:
            checkpoint_dict.update(others)
        
        # 保存路径格式：model_step.pt 或 model_step_best.pt
        ckpt_path = os.path.join(self.save_dir, 'model_{:d}.pt'.format(step))
        
        # 保存模型
        torch.save(checkpoint_dict, ckpt_path)
        
        # 更新检查点记录
        self.checkpoints[step] = (metric_value, time.time(), ckpt_path)
        
        # 检查是否需要保存为最佳模型
        best_step = min(self.checkpoints.keys(), key=lambda s: self.checkpoints[s][0])
        best_ckpt_path = os.path.join(self.save_dir, 'model_best.pt')
        
        # 如果当前模型是最佳的（JSD值最小），创建一个链接或复制
        if step == best_step:
            if os.path.exists(best_ckpt_path):
                os.remove(best_ckpt_path)
            shutil.copy2(ckpt_path, best_ckpt_path)
        
        # 如果保存的检查点过多，删除旧的非最佳检查点
        if self.max_to_keep is not None and len(self.checkpoints) > self.max_to_keep:
            # 按步骤排序，删除最早的检查点（保留最近的 max_to_keep 个和最佳模型）
            steps_sorted = sorted(self.checkpoints.keys())  # 按步骤从小到大排序
            best_step = min(self.checkpoints.keys(), key=lambda s: self.checkpoints[s][0])
            
            # 计算需要删除的步骤：保留最近的 max_to_keep 个和最佳模型
            steps_to_keep = set(steps_sorted[-self.max_to_keep:] + [best_step])
            steps_to_remove = [s for s in steps_sorted if s not in steps_to_keep]
            
            for step_to_remove in steps_to_remove:
                _, _, ckpt_path = self.checkpoints[step_to_remove]
                if os.path.exists(ckpt_path):
                    os.remove(ckpt_path)
                del self.checkpoints[step_to_remove]
        
        return ckpt_path


def seed_all(seed):
    torch.manual_seed(seed)
    np.random.seed(seed)
    random.seed(seed)


def get_logger(name, log_dir=None):
    logger = logging.getLogger(name)
    logger.setLevel(logging.DEBUG)
    formatter = logging.Formatter('[%(asctime)s::%(name)s::%(levelname)s] %(message)s')

    stream_handler = logging.StreamHandler()
    stream_handler.setLevel(logging.DEBUG)
    stream_handler.setFormatter(formatter)
    logger.addHandler(stream_handler)

    if log_dir is not None:
        file_handler = logging.FileHandler(os.path.join(log_dir, 'log.txt'))
        file_handler.setLevel(logging.INFO)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

    return logger


def get_new_log_dir(root='./logs', postfix='', prefix=''):
    log_dir = os.path.join(root, prefix + time.strftime('%Y_%m_%d__%H_%M_%S', time.localtime()) + postfix)
    os.makedirs(log_dir)
    return log_dir


def int_tuple(argstr):
    return tuple(map(int, argstr.split(',')))


def str_tuple(argstr):
    return tuple(argstr.split(','))


def int_list(argstr):
    return list(map(int, argstr.split(',')))


def str_list(argstr):
    return list(argstr.split(','))


def log_hyperparams(writer, args):
    from torch.utils.tensorboard.summary import hparams
    vars_args = {k:v if isinstance(v, str) else repr(v) for k, v in vars(args).items()}
    exp, ssi, sei = hparams(vars_args, {})
    writer.file_writer.add_summary(exp)
    writer.file_writer.add_summary(ssi)
    writer.file_writer.add_summary(sei)

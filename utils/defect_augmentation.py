# defect_augment.py

import torch
import random
import open3d as o3d
import numpy as np
from typing import Tuple, Dict

# 任务1：从你的 dataset.py 中导入高效的几何特征计算函数
# 假设 defect_augment.py 与 utils/ 目录在同一层级或已配置好PYTHONPATH
try:
    from utils.dataset import estimate_geometry_features
except ImportError:
    print("警告: 无法从 utils.dataset 导入 estimate_geometry_features。")
    print("请确保 PYTHONPATH 设置正确，或将此文件放在合适的目录下。")
    # 作为后备，可以保留一个简单的实现，但这不推荐
    def estimate_geometry_features(points, k=20):
        raise NotImplementedError("请修复导入问题，以使用高效的几何特征计算。")


class RealisticDefectAugmenter:
    """
    一个用于生成更真实、平滑的局部点云缺陷的增强器。
    核心思想是创建一个平滑衰减的、方向一致的局部形变场。
    """

    def __init__(self, device: str = 'cuda' if torch.cuda.is_available() else 'cpu'):
        """
        初始化增强器。
        Args:
            device (str): 计算设备 ('cuda' or 'cpu').
        """
        self.device = device
        print(f"RealisticDefectAugmenter 初始化，使用设备: {self.device}")

    def _smooth_falloff(self, dist_ratio: torch.Tensor) -> torch.Tensor:
        """
        计算平滑衰减权重。使用 (1 - x^2)^3 形式的函数。
        该函数在 x=0 时为 1，在 x=1 时为 0，且在 x=1 处的导数为 0，保证平滑过渡。
        
        Args:
            dist_ratio (torch.Tensor): 归一化的距离 (距离/半径)，范围 [0, 1]。
        
        Returns:
            torch.Tensor: 衰减权重，范围 [0, 1]。
        """
        clamped_dist = torch.clamp(dist_ratio, 0.0, 1.0)
        weights = (1 - clamped_dist**2)**3
        return weights

    def add_defect(self,
                   points: torch.Tensor,
                   defect_type: str,
                   radius_frac: Tuple[float, float] = (0.08, 0.15),
                   max_disp_frac: Tuple[float, float] = (0.01, 0.03),
                   core_patch_size: int = 10) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        向点云添加一个单一的、真实的凹陷或凸起缺陷。

        Args:
            points (torch.Tensor): 输入点云 [N, 3]，应已归一化到单位球或包围盒内。
            defect_type (str): 'sink' (凹陷) 或 'bulge' (凸起)。
            radius_frac (Tuple[float, float]): 缺陷半径范围，相对于点云包围盒对角线长度的比例。
            max_disp_frac (Tuple[float, float]): 在缺陷中心的最大位移量范围，同样是相对于包围盒对角线。
            core_patch_size (int): 用于定义“形变核心”的点簇大小。

        Returns:
            Tuple[torch.Tensor, torch.Tensor]:
                - points_new (torch.Tensor): 带有缺陷的新点云 [N, 3]。
                - mask (torch.Tensor): 标记了哪些点被移动的布尔掩码 [N,]。
        """
        if defect_type not in ['sink', 'bulge']:
            raise ValueError(f"不支持的缺陷类型: {defect_type}。仅支持 'sink' 或 'bulge'。")

        points = points.to(self.device)
        N = points.shape[0]

        # --- 1. 计算必要的几何属性 ---
        try:
            # 使用从 dataset.py 导入的高效函数
            normals, _ = estimate_geometry_features(points, k=20)
        except Exception as e:
            print(f"错误: 几何特征计算失败: {e}。跳过增强。")
            return points.cpu(), torch.zeros(N, dtype=torch.bool)
        
        # 如果法线计算失败（例如，点太少），则返回原始点云
        if torch.all(normals == 0):
            print("警告: 法线计算返回全零，可能因点数不足。跳过增强。")
            return points.cpu(), torch.zeros(N, dtype=torch.bool)
            
        # --- 2. 随机选择缺陷参数和中心 ---
        bbox_min = torch.min(points, dim=0)[0]
        bbox_max = torch.max(points, dim=0)[0]
        bbox_diag = torch.norm(bbox_max - bbox_min)

        radius = bbox_diag * random.uniform(*radius_frac)
        max_displacement = bbox_diag * random.uniform(*max_disp_frac)
        direction = -1.0 if defect_type == 'sink' else 1.0

        # 随机选择一个点作为初始中心
        center_idx = random.randint(0, N - 1)
        p_center_initial = points[center_idx]

        # --- 3. 定义“形变核心”并计算其平均属性 ---
        # 找到初始中心点附近的点簇作为形变核心
        distances_to_center = torch.norm(points - p_center_initial, dim=1)
        # .topk 需要 k <= N
        k = min(core_patch_size, N)
        _, core_indices = torch.topk(distances_to_center, k=k, largest=False)
        
        # 计算核心区域的平均位置和平均法线
        core_patch_points = points[core_indices]
        core_patch_normals = normals[core_indices]
        
        p_core_avg = torch.mean(core_patch_points, dim=0)
        n_core_avg = torch.mean(core_patch_normals, dim=0)
        n_core_avg = n_core_avg / (torch.norm(n_core_avg) + 1e-8) # 归一化平均法线

        # --- 4. 识别影响区域并计算位移 ---
        # 找到在“形变核心”平均位置半径范围内的所有点
        distances_to_core = torch.norm(points - p_core_avg, dim=1)
        affected_mask = distances_to_core < radius
        
        if not torch.any(affected_mask):
            print("警告: 缺陷半径内没有点，跳过增强。")
            return points.cpu(), torch.zeros(N, dtype=torch.bool)

        affected_indices = torch.where(affected_mask)[0]
        
        # 计算影响区域内各点的位移
        dist_ratio = distances_to_core[affected_indices] / radius
        falloff_weights = self._smooth_falloff(dist_ratio)
        
        # 所有受影响的点都沿着“核心平均法线”方向位移
        # 这确保了形变的光滑和整体性
        displacement_vectors = direction * max_displacement * falloff_weights.unsqueeze(1) * n_core_avg.unsqueeze(0)

        # --- 5. 应用位移并返回结果 ---
        points_new = points.clone()
        points_new[affected_indices] += displacement_vectors
        
        return points_new.cpu(), affected_mask.cpu()
# defect_augmentation_v2.py
"""
Physically‑based Dent & Bulge augmenter
Author: ChatGPT‑o3   2025‑08‑08
"""

from __future__ import annotations
import random, math
from typing import Tuple

import torch
import numpy as np
import open3d as o3d


# ---------- 基础工具 ----------
def tensor_to_o3d(t: torch.Tensor) -> o3d.geometry.PointCloud:
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(t.cpu().numpy())
    return pcd


def estimate_normals(pts: torch.Tensor, k: int = 30) -> torch.Tensor:
    """KNN 法向估计，返回 (N,3) numpy"""
    pcd = tensor_to_o3d(pts)
    pcd.estimate_normals(
        search_param=o3d.geometry.KDTreeSearchParamKNN(knn=k),
        fast_normal_computation=True,
    )
    return torch.from_numpy(np.asarray(pcd.normals)).to(pts.device)


# ---------- Augmenter ----------
class PhysicallyBasedDentBulgeAugmenter:
    """
    • 位移沿 **每个点自身法向** 施加  
    • 位移幅值 ~  max_disp · exp(‑r_ell² / (2σ²))，C¹ 连续  
    • 支持椭圆异向半径与粗糙度扰动
    """

    def __init__(
        self,
        device: str | torch.device = "cuda" if torch.cuda.is_available() else "cpu",
    ):
        self.device = torch.device(device)
        print(f"[Augmenter‑V3] Physically‑based dent/bulge, device = {self.device}")

    # ---------- PUBLIC ----------
    @torch.no_grad()
    def add_defect(
        self,
        points: torch.Tensor,                  # (N,3), float32/64, cpu or cuda
        defect_type: str = "bulge",            # 'bulge' | 'sink'
        radius_frac: Tuple[float, float] = (0.04, 0.08),
        max_disp_frac: Tuple[float, float] = (0.015, 0.04),
        anisotropy_range: Tuple[float, float] = (0.5, 1.5),   # a/b 比例
        roughness: float = 0.15,               # 0=光滑，0.15≈常见
        flatness_thresh: float = 0.06,         # 曲率阈，过滤尖锐区域
        normal_k: int = 30,
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Returns
        -------
        new_points : Tensor (N,3)
        mask       : BoolTensor (N,)  受影响的点
        """
        if defect_type not in ("bulge", "sink"):
            raise ValueError("defect_type must be 'bulge' or 'sink'")

        pts = points.to(self.device)
        N   = pts.shape[0]

        # ① 预计算法向 & 曲率
        normals = estimate_normals(pts, k=normal_k)          # (N,3)
        # 近似曲率 = PCA 最小特征值 / (总特征值)
        curv = self._approx_curvature(pts, k=normal_k)        # (N,)

        # 在平坦区域随机选中心
        flat_ids = torch.nonzero(curv < flatness_thresh).squeeze(1)
        if flat_ids.numel() == 0:            # 如果对象本身曲率都高，退化为全局采样
            flat_ids = torch.arange(N, device=pts.device)
        c_idx = flat_ids[torch.randint(0, flat_ids.numel(), (1,))].item()
        center = pts[c_idx]                                  # (3,)

        # ② 随机超参数
        bb_min, _ = pts.min(0)
        bb_max, _ = pts.max(0)
        diag = torch.linalg.norm(bb_max - bb_min)
        radius   = diag * random.uniform(*radius_frac)
        max_disp = diag * random.uniform(*max_disp_frac)
        sigma    = radius * 0.45                             # σ ≈ 0.45·R
        direction = 1.0 if defect_type == "bulge" else -1.0

        # 椭圆异向缩放
        a = radius
        ratio = random.uniform(*anisotropy_range)            # a/b
        b = radius * ratio

        # ③ 局部坐标系 (PCA 取两主轴)
        neigh_ids = self._knn(pts, center, k=normal_k)
        local_axes = self._principal_axes(pts[neigh_ids])    # (3,3)
        # 保证右手系
        if torch.linalg.det(local_axes) < 0:
            local_axes[:, -1].mul_(-1)

        uvw = (pts - center) @ local_axes                    # (N,3)
        uv = uvw[:, :2]                                      # xy in local plane
        # 椭圆距离 r_ell
        r_ell = torch.sqrt((uv[:, 0] / a) ** 2 + (uv[:, 1] / b) ** 2)  # (N,)

        mask = r_ell < 1.0
        if mask.sum() == 0:
            print("[Augmenter‑V3] radius too small, no points affected.")
            return pts.cpu(), torch.zeros(N, dtype=torch.bool)

        # ④ 位移幅值：高斯 + 粗糙噪声
        base_amp = torch.exp(-(r_ell[mask] ** 2) / (2 * (sigma / radius) ** 2))
        # 加一点细粒度噪声（正态 0‑1），再限制幅度
        noise = (torch.randn_like(base_amp) * roughness).clamp(-0.4, 0.4)
        amp = direction * max_disp * base_amp * (1 + noise)

        # ⑤ 按每点法向位移
        disp = amp.unsqueeze(1) * normals[mask]              # (M,3)
        pts_new = pts.clone()
        pts_new[mask] += disp

        return pts_new.cpu(), mask.cpu()

    # ---------- internal ----------
    @staticmethod
    def _knn(pts: torch.Tensor, target: torch.Tensor, k: int = 30):
        d = torch.linalg.norm(pts - target, dim=1)
        return torch.topk(d, k=k, largest=False).indices

    @staticmethod
    def _principal_axes(neigh: torch.Tensor):
        mu = neigh.mean(0)
        X = neigh - mu
        cov = X.T @ X / max(1, neigh.shape[0] - 1)
        _, vecs = torch.linalg.eigh(cov)     # 升序
        return torch.flip(vecs, dims=[1])    # 降序 (col=主轴)

    def _approx_curvature(self, pts: torch.Tensor, k: int = 30):
        # 粗略曲率 = 最小特征值 / 总和
        neigh_ids = [self._knn(pts, pts[i], k) for i in range(pts.shape[0])]
        curv = torch.zeros(pts.shape[0], device=pts.device)
        for i, idx in enumerate(neigh_ids):
            X = pts[idx] - pts[i]
            cov = X.T @ X / max(1, k - 1)
            eigvals = torch.linalg.eigvalsh(cov)
            curv[i] = eigvals.min() / eigvals.sum().clamp(min=1e-6)
        return curv
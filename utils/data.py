import torch
from torch.utils.data import DataLoader, random_split


def get_train_val_test_datasets(dataset, train_ratio, val_ratio):
    assert (train_ratio + val_ratio) <= 1
    train_size = int(len(dataset) * train_ratio)
    val_size = int(len(dataset) * val_ratio)
    test_size = len(dataset) - train_size - val_size
    
    train_set, val_set, test_set = random_split(dataset, [train_size, val_size, test_size])
    return train_set, val_set, test_set


def get_train_val_test_loaders(dataset, train_ratio, val_ratio, train_batch_size, val_test_batch_size, num_workers):
    train_set, val_set, test_set = get_train_val_test_datasets(dataset, train_ratio, val_ratio)

    train_loader = DataLoader(train_set, train_batch_size, shuffle=True, num_workers=num_workers)
    val_loader = DataLoader(val_set, val_test_batch_size, shuffle=False, num_workers=num_workers)
    test_loader = DataLoader(test_set, val_test_batch_size, shuffle=False, num_workers=num_workers)
    
    return train_loader, val_loader, test_loader


def get_data_iterator(iterable):
    """Allows training with DataLoaders in a single infinite loop:
        for i, data in enumerate(inf_generator(train_loader)):
    """
    iterator = iterable.__iter__()
    while True:
        try:
            yield iterator.__next__()
        except StopIteration:
            iterator = iterable.__iter__()


def custom_collate_fn(batch):
    """
    自定义的 collate_fn 函数，可以处理包含 None 值的批次
    
    Args:
        batch: 一个批次的数据列表
        
    Returns:
        合并后的批次数据
    """
    # 过滤掉为 None 的样本
    batch = [b for b in batch if b is not None]
    
    if len(batch) == 0:
        return None
    
    elem = batch[0]
    elem_type = type(elem)
    
    if elem_type.__module__ == 'numpy' and elem_type.__name__ != 'str_' \
            and elem_type.__name__ != 'string_':
        if elem_type.__name__ == 'ndarray' or elem_type.__name__ == 'memmap':
            # 处理 numpy 数组
            return torch.stack([torch.from_numpy(b) for b in batch], 0)
        
    elif isinstance(elem, torch.Tensor):
        # 处理 PyTorch 张量
        return torch.stack(batch, 0)
    
    elif isinstance(elem, (int, float, str, bytes)):
        # 处理标量
        return batch
    
    elif isinstance(elem, dict):
        # 处理字典
        result = {}
        for key in elem:
            # 检查所有样本在该键下是否都有有效值（非None）
            if all(d.get(key) is not None for d in batch):
                values = [d[key] for d in batch]
                result[key] = custom_collate_fn(values)
        return result
    
    elif isinstance(elem, (list, tuple)):
        # 处理列表和元组
        transposed = zip(*batch)
        return elem_type([custom_collate_fn(samples) for samples in transposed])
    
    # 其他类型直接返回
    return batch

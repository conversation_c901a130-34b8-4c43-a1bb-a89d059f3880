import os
import random
from copy import copy
import torch
from torch.utils.data import Dataset
import numpy as np
import h5py
from tqdm.auto import tqdm
import open3d as o3d
from torch.utils.data import DataLoader
from torchvision import transforms
import torch_geometric
import torch_cluster
import glob
from utils.transform import NormalizeScale, PairPCARotate

def farthest_point_sampling(points, npoint):
    """
    使用 torch_cluster 进行 FPS 采样, 处理点数不足或需要上采样的情况。
    Args:
        points (torch.Tensor): 输入点云 [N, 3]
        npoint (int): 目标点数
    Returns:
        torch.Tensor: 采样后的点云 [npoint, 3]
    """
    N = points.size(0)
    if N == npoint:
        return points
    elif N < npoint:
        # 如果点数不足，则重复采样
        repeats = (npoint + N - 1) // N
        indices = torch.arange(N, device=points.device).repeat(repeats)[:npoint]
        return points[indices]
    else: # N > npoint (Downsampling needed)
        # 计算采样比例
        ratio = npoint / N
        indices = torch_cluster.fps(points, ratio=ratio)

        # 确保返回正确的点数 (torch_cluster.fps 可能因实现返回稍多或稍少的点)
        if len(indices) < npoint:
            # 如果采样点数不够，则在剩余点中随机选择补充
            mask = torch.ones(N, dtype=torch.bool, device=points.device)
            mask[indices] = False
            num_needed = npoint - len(indices)

            available_indices = torch.where(mask)[0]
            if len(available_indices) >= num_needed:
                 # 从可用点中随机选
                 extra_indices = available_indices[torch.randperm(len(available_indices), device=points.device)[:num_needed]]
            else:
                 # 如果剩余点也不够，则重复采样已选点
                 extra_indices = indices[torch.randint(0, len(indices), (num_needed,), device=points.device)]

            indices = torch.cat([indices, extra_indices], dim=0)

        elif len(indices) > npoint:
            indices = indices[:npoint] # 确保不多于 npoint

        return points[indices]

def estimate_geometry_features(points, k=20, eps=1e-8):
    """
    估计法向量和曲率 (基于论文公式 1 和 2的3D扩展)
    Args:
        points (torch.Tensor): 输入点云 [N, 3]
        k (int): 近邻点数量
        eps (float): 防止除零的小常数
    Returns:
        tuple: (normals [N, 3], curvatures [N, 1])
    """
    num_points = points.shape[0]
    device = points.device

    if num_points <= k: # 如果点数太少，无法可靠计算
        # 返回零向量和零曲率
        print(f"警告: 点数 ({num_points}) 不足 k ({k})，无法计算几何特征。返回零值。")
        return torch.zeros_like(points), torch.zeros(num_points, 1, device=device)

    # 1. 找到k近邻 (使用 torch_geometric)
    edge_index = torch_geometric.nn.knn_graph(points, k=k, loop=True, batch=None)
    row, col = edge_index

    # 2. 计算局部协方差矩阵
    neighbors = points[col].view(num_points, k, 3)
    centered_neighbors = neighbors - points.unsqueeze(1)
    cov_matrix = torch.bmm(centered_neighbors.transpose(1, 2), centered_neighbors) / k

    # 3. 计算特征值和特征向量 (法线)
    try:
        eigenvalues, eigenvectors = torch.linalg.eigh(cov_matrix) # eigenvalues 升序排列
    except torch._C._LinAlgError:
         print(f"警告: eigh 分解失败，点数: {num_points}, k: {k}")
         return torch.zeros_like(points), torch.zeros(num_points, 1, device=device)

    normals = eigenvectors[:, :, 0] # 最小特征值对应的特征向量 [N, 3]

    # --- 调整法向量方向 ---
    centroid = points.mean(dim=0, keepdim=True)
    direction_to_centroid = points - centroid
    dot_product = torch.sum(normals * direction_to_centroid, dim=1)
    flip_mask = dot_product < 0
    normals[flip_mask] = -normals[flip_mask]
    # --- 法线方向调整结束 ---

    # 4. 计算曲率 (使用 3D 曲率定义: lambda_min / sum(lambdas))
    lambda1 = eigenvalues[:, 0]
    lambda2 = eigenvalues[:, 1]
    lambda3 = eigenvalues[:, 2]
    curvatures = lambda1 / (lambda1 + lambda2 + lambda3 + eps) # [N]
    curvatures = curvatures.unsqueeze(1) # [N, 1]

    # 裁剪曲率值，避免极端值影响后续计算
    curvatures = torch.clamp(curvatures, 0.0, 1.0) # 通常曲率在[0, 1/3]或类似范围

    return normals, curvatures

def geometry_aware_point_sampling(points, npoint, tau=0.7, k=20, eps=1e-8):
    """
    几何感知点云采样 (GPS) - 优先选择几何变化大的点。
    Args:
        points (torch.Tensor): 输入点云 [N, 3]
        npoint (int): 目标采样点数
        tau (float): 选择高变化率点的比例阈值 (0 < tau <= 1)
        k (int): 计算几何特征时的近邻数
        eps (float): 防止除零的小常数
    Returns:
        torch.Tensor: 采样后的点云 [npoint, 3]
    """
    N = points.shape[0]
    device = points.device

    if N <= npoint:
        # 点数不足，直接调用 FPS 处理（它内部会处理重复采样）
        return farthest_point_sampling(points, npoint)

    # 1. 计算几何特征 (法线 N_i 和曲率 K_i)
    normals, curvatures = estimate_geometry_features(points, k=k, eps=eps) # [N, 3], [N, 1]

    # 如果特征计算失败（例如点数不足），回退到 FPS
    if torch.all(normals == 0):
        print("警告: 几何特征计算失败，回退到 FPS。")
        return farthest_point_sampling(points, npoint)

    # 2. 计算变化率 (R_norm, R_curv, R_i)
    edge_index = torch_geometric.nn.knn_graph(points, k=k, loop=False, batch=None) # loop=False 排除自身
    row, col = edge_index # row: 中心点索引, col: 邻居索引

    # 检查是否有边（如果k=0或点太少可能没有）
    if edge_index.shape[1] == 0:
        print("警告: 未找到邻居边，无法计算变化率，回退到 FPS。")
        return farthest_point_sampling(points, npoint)

    # 获取邻居和中心点的特征
    neighbors_normals = normals[col]
    neighbors_curvatures = curvatures[col]
    center_normals = normals[row]
    center_curvatures = curvatures[row]

    # 计算点间向量和距离
    v_ij = points[col] - points[row]
    dist_ij = torch.norm(v_ij, dim=1, keepdim=True).clamp(min=eps) # 使用 clamp 替代 +eps

    # 计算 R_norm 和 R_curv
    norm_diff = torch.norm(center_normals - neighbors_normals, dim=1, keepdim=True)
    R_norm = norm_diff / dist_ij
    R_curv = torch.abs(center_curvatures - neighbors_curvatures)

    # 计算每个点的综合变化率 R_i
    combined_rate = R_norm + R_curv
    R_i = torch.zeros(N, 1, device=device)
    R_i.scatter_add_(0, row.unsqueeze(1), combined_rate) # 使用 scatter_add_ 高效聚合

    # 计算度数并求平均
    degrees = torch.zeros(N, dtype=R_i.dtype, device=device) # 使用与 R_i 相同的 dtype
    ones = torch.ones_like(row, dtype=R_i.dtype)
    degrees.scatter_add_(0, row, ones)
    degrees = degrees.unsqueeze(1).clamp(min=1) # 保证度数至少为1
    R_i = R_i / degrees

    # 3. 几何感知采样
    # 对 R_i 进行排序，获取高变化率点的索引
    sorted_indices = torch.argsort(R_i.squeeze(), descending=True)

    # 选择排名靠前的点
    num_high_roc_points = max(1, int(N * tau)) # 确保至少选择一个点
    high_roc_indices = sorted_indices[:num_high_roc_points]

    # 4. 组合采样策略以获得 npoint 个点
    if num_high_roc_points >= npoint:
        # 如果高变化率点足够多，直接从中用 FPS 选择 npoint 个
        high_roc_points_tensor = points[high_roc_indices]
        num_to_sample_fps = min(npoint, num_high_roc_points)
        ratio_fps = num_to_sample_fps / num_high_roc_points if num_high_roc_points > 0 else 0
        if ratio_fps > 0:
             indices_in_high_roc = torch_cluster.fps(high_roc_points_tensor, ratio=ratio_fps)
             indices_in_high_roc = indices_in_high_roc[:num_to_sample_fps] # 确保数量
             sampled_indices_final = high_roc_indices[indices_in_high_roc]
             # ---!!! 添加下面这行 !!!---
             sampled_points_final = points[sampled_indices_final] # 根据最终索引获取点云
             # ---!!! 添加结束 !!!---
        else:
             print("警告: num_high_roc_points 计算异常，回退到 FPS。")
             # 如果这里回退，也需要赋值
             sampled_points_final = farthest_point_sampling(points, npoint) # 使用 FPS 获取点云

    else:
        # 如果高变化率点不够 npoint 个，则全部选中
        # ... (这里的逻辑已经正确地为 sampled_points_final 赋值了) ...
        # ... (保留 else 分支的现有代码) ...
        high_roc_sampled_indices = high_roc_indices
        num_needed = npoint - num_high_roc_points
        mask = torch.ones(N, dtype=torch.bool, device=device)
        mask[high_roc_sampled_indices] = False
        remaining_points = points[mask]

        if remaining_points.shape[0] > 0:
            num_to_sample_extra = min(num_needed, remaining_points.shape[0])
            extra_points = farthest_point_sampling(remaining_points, num_to_sample_extra)
            sampled_points_combined = torch.cat([points[high_roc_sampled_indices], extra_points], dim=0)
            if sampled_points_combined.shape[0] < npoint:
                 sampled_points_final = farthest_point_sampling(sampled_points_combined, npoint)
            else:
                 # 如果点数可能超过 npoint，也需要用 FPS 修正
                 sampled_points_final = farthest_point_sampling(sampled_points_combined, npoint)
        else:
            sampled_points_final = farthest_point_sampling(points[high_roc_sampled_indices], npoint)

    # 最终确保返回的点数为 npoint
    # 在引用 sampled_points_final 之前，它现在肯定已经被赋值了
    if sampled_points_final.shape[0] != npoint:
        # print(f"调试: GPS采样后点数 {sampled_points_final.shape[0]} vs {npoint}，进行最后调整。")
        sampled_points_final = farthest_point_sampling(sampled_points_final, npoint)

    return sampled_points_final

def hybrid_gps_fps_sampling(points, npoint, gps_ratio=0.8, k=20, eps=1e-8):
    """
    混合采样：结合 GPS 选出的高变化率点和全局 FPS 选出的均匀点。
    Args:
        points (torch.Tensor): 输入点云 [N, 3]
        npoint (int): 目标采样点数
        gps_ratio (float): 使用 GPS 选择的点的比例 (0 < gps_ratio < 1)
        k (int): 计算几何特征时的近邻数
        eps (float): 防止除零的小常数
    Returns:
        torch.Tensor: 采样后的点云 [npoint, 3]
    """
    N = points.shape[0]
    device = points.device

    if N <= npoint:
        return farthest_point_sampling(points, npoint)

    # --- 1. GPS 部分：获取高变化率点的索引 ---
    n_gps = max(1, int(npoint * gps_ratio))
    # 计算几何特征和变化率 R_i (与 geometry_aware_point_sampling 中类似)
    normals, curvatures = estimate_geometry_features(points, k=k, eps=eps)
    if torch.all(normals == 0): # 特征计算失败则回退
         print("警告 (Hybrid): 几何特征计算失败，回退到 FPS。")
         return farthest_point_sampling(points, npoint)

    edge_index = torch_geometric.nn.knn_graph(points, k=k, loop=False, batch=None)
    if edge_index.shape[1] == 0:
         print("警告 (Hybrid): 未找到邻居边，回退到 FPS。")
         return farthest_point_sampling(points, npoint)
    row, col = edge_index
    neighbors_normals = normals[col]
    neighbors_curvatures = curvatures[col]
    center_normals = normals[row]
    center_curvatures = curvatures[row]
    v_ij = points[col] - points[row]
    dist_ij = torch.norm(v_ij, dim=1, keepdim=True).clamp(min=eps)
    norm_diff = torch.norm(center_normals - neighbors_normals, dim=1, keepdim=True)
    R_norm = norm_diff / dist_ij
    R_curv = torch.abs(center_curvatures - neighbors_curvatures)
    combined_rate = R_norm + R_curv
    R_i = torch.zeros(N, 1, device=device, dtype=combined_rate.dtype)
    R_i.scatter_add_(0, row.unsqueeze(1), combined_rate)
    degrees = torch.zeros(N, dtype=R_i.dtype, device=device)
    ones = torch.ones_like(row, dtype=R_i.dtype)
    degrees.scatter_add_(0, row, ones)
    degrees = degrees.unsqueeze(1).clamp(min=1)
    R_i = R_i / degrees
    # 获取变化率最高的 n_gps 个点的索引
    sorted_indices = torch.argsort(R_i.squeeze(), descending=True)
    idx_gps = sorted_indices[:n_gps]

    # --- 2. 全局 FPS 部分：获取均匀分布的 npoint 个点的索引 ---
    # 注意：这里我们需要 FPS 返回索引，而不是点本身
    # 临时方法：用 torch_cluster.fps 获取索引
    ratio_fps_all = npoint / N
    idx_fps_all = torch_cluster.fps(points, ratio=ratio_fps_all)
    # 确保至少有 npoint 个索引 (如果 torch_cluster 返回稍少)
    if len(idx_fps_all) < npoint:
         # 如果不够，补充随机索引（排除已选的 FPS 索引）
         mask = torch.ones(N, dtype=torch.bool, device=device)
         mask[idx_fps_all] = False
         num_needed = npoint - len(idx_fps_all)
         available_indices = torch.where(mask)[0]
         if len(available_indices) >= num_needed:
              extra_indices = available_indices[torch.randperm(len(available_indices), device=device)[:num_needed]]
         else: # 剩余点也不够，只能重复已选的 FPS 索引
              extra_indices = idx_fps_all[torch.randint(0, len(idx_fps_all), (num_needed,), device=device)]
         idx_fps_all = torch.cat([idx_fps_all, extra_indices])
    idx_fps_all = idx_fps_all[:npoint] # 确保不超过 npoint

    # --- 3. 合并与最终选择 ---
    combined_idx = torch.cat((idx_gps, idx_fps_all))
    unique_idx = torch.unique(combined_idx)

    # --- 4. 在合并后的唯一索引对应的点上进行最终 FPS ---
    # 这步是为了精确控制到 npoint，并进一步优化分布
    points_to_sample_from = points[unique_idx]
    final_sampled_points = farthest_point_sampling(points_to_sample_from, npoint)

    return final_sampled_points

# --- Real3D-AD Dataset Class ---


    def __len__(self):
        return len(self.pointclouds)

    def __getitem__(self, idx):
        # 深拷贝以避免多进程或转换时修改原始缓存数据
        data = {k: v.clone() if isinstance(v, torch.Tensor) else copy(v) for k, v in self.pointclouds[idx].items()}

        if self.transform is not None:
            data = self.transform(data) # 假设 transform 能处理字典输入或只修改 'pointcloud'

        return data
class Real3DADDataset(Dataset):
    """
    读取 Real3D-AD 原始 .pcd 文件，并按需做
      - bbox/unit 归一化
      - 采样 (FPS / GPS / Hybrid)
      - 任意自定义 transform
    """
    def __init__(self,
                 path,
                 categories,
                 split,
                 scale_mode='shape_bbox',
                 transform=None,
                 num_points=2048,          # 设 None 表示“不采样，保留原分辨率”
                 sampling_method='hybrid'  # random / fps / gps / hybrid / none
                 ):
        super().__init__()

        assert isinstance(categories, list), "`categories` 必须是 list"
        assert split in ('train', 'test')
        assert scale_mode is None or scale_mode in (
            'global_unit', 'shape_unit', 'shape_bbox',
            'shape_half', 'shape_34')
        assert sampling_method in (
            'random', 'fps', 'gps', 'hybrid', 'none'), \
            f"不支持的采样方法: {sampling_method}"

        self.path           = path
        self.categories     = categories
        self.split          = split
        self.scale_mode     = scale_mode
        self.transform      = transform
        self.num_points     = num_points      # 可能是 None
        self.sampling_method= sampling_method
        self.pointclouds    = []

        print("初始化 Real3DADDataset:")
        print(f"  – 根目录:      {path}")
        print(f"  – 类别:        {categories}")
        print(f"  – 分割:        {split}")
        print(f"  – 归一化模式:  {scale_mode}")
        print(f"  – 采样方法:    {sampling_method}")
        print(f"  – 目标点数:    {num_points}")

        self._load()

    # ------------------------------------------------------------------ #
    def _load(self):
        """遍历目录，把合格的点云缓存进 self.pointclouds"""
        eps = 1e-8
        print(f"加载 {self.split} 数据…")

        for cate in self.categories:
            cate_dir = os.path.join(self.path, cate, self.split)
            for fname in os.listdir(cate_dir):
                if not fname.endswith('.pcd'):
                    continue

                fpath = os.path.join(cate_dir, fname)
                pc_np = np.asarray(o3d.io.read_point_cloud(fpath).points,
                                   dtype=np.float32)
                pc    = torch.from_numpy(pc_np)              # (N, 3)  float32

                # ---------- 归一化（可选） ----------
                shift = torch.zeros((1, 3))
                scale = torch.ones ((1, 3))
                if self.scale_mode == 'shape_unit':
                    shift      = pc.mean(0, keepdim=True)
                    scale_val  = pc.flatten().std().view(1, 1) * 2
                    pc         = (pc - shift) / (scale_val + eps)
                    scale.fill_(scale_val.item() + eps)
                elif self.scale_mode == 'shape_bbox':
                    pc_max, _  = pc.max(0, keepdim=True)
                    pc_min, _  = pc.min(0, keepdim=True)
                    shift      = (pc_min + pc_max) / 2                     # bbox 中心
                    scale_val  = (pc_max - pc_min).max().view(1, 1) / 2
                    pc         = (pc - shift) / (scale_val + eps)
                    scale.fill_(scale_val.item() + eps)
                elif self.scale_mode not in (None, 'shape_bbox', 'shape_unit'):
                    print(f"警告: scale_mode '{self.scale_mode}' 未实现，跳过归一化")

                # ---------- 采样（当且仅当 num_points 有值） ----------
                sampled_pc = pc
                if self.num_points is not None and self.sampling_method != 'none':
                    N = pc.size(0)
                    if N != self.num_points:
                        if self.sampling_method == 'random':
                            if N > self.num_points:
                                idx = torch.randperm(N)[:self.num_points]
                            else:
                                repeats = (self.num_points + N - 1) // N
                                idx = torch.arange(N).repeat(repeats)[:self.num_points]
                            sampled_pc = pc[idx]

                        elif self.sampling_method == 'fps':
                            sampled_pc = farthest_point_sampling(pc, self.num_points)

                        elif self.sampling_method == 'gps':
                            sampled_pc = geometry_aware_point_sampling(
                                pc, self.num_points, tau=0.3, k=20)

                        elif self.sampling_method == 'hybrid':
                            sampled_pc = hybrid_gps_fps_sampling(
                                pc, self.num_points, gps_ratio=0.8, k=20)

                        # 二次核对
                        if sampled_pc.shape[0] != self.num_points:
                            print(f"警告: {fname} 采样后点数 {sampled_pc.shape[0]} "
                                  f"≠ {self.num_points}，用 FPS 兜底")
                            sampled_pc = farthest_point_sampling(sampled_pc,
                                                                 self.num_points)

                # ---------- 最终有效性检查 ----------
                ok_shape = (self.num_points is None or
                            sampled_pc.shape[0] == self.num_points)
                if ok_shape and sampled_pc.shape[1] == 3:
                    self.pointclouds.append({
                        'pointcloud': sampled_pc.contiguous(),
                        'category'  : cate,
                        'id'        : fname,
                        'filename'  : fname,
                        'path'      : fpath,
                        'shift'     : shift.contiguous(),
                        'scale'     : scale.contiguous(),
                    })
                else:
                    print(f"跳过 {fname}: 最终形状 {sampled_pc.shape}")

        print(f"加载完成，总计 {len(self.pointclouds)} 个点云")

    # ------------------------------------------------------------------ #
    def __len__(self):
        return len(self.pointclouds)

    def __getitem__(self, idx):
        # 深拷贝避免多进程改动底层缓存
        item = {k: (v.clone() if torch.is_tensor(v) else copy(v))
                for k, v in self.pointclouds[idx].items()}

        if self.transform is not None:
            item = self.transform(item)

        return item

class PrecomputedDefectDataset(Dataset):
    """
    加载预先生成的缺陷样本，并对其与对应的正常样本进行统一、一致的数据处理。
    处理流程严格遵循：归一化 -> 对齐 -> 采样。
    """
    def __init__(self, root_dir: str, raw_data_source: Real3DADDataset, 
                 scale_mode: str, num_points: int, sampling_method: str, 
                 use_augmentation: bool = False):
        """
        Args:
            root_dir (str): 预计算缺陷样本的根目录。
            raw_data_source (Real3DADDataset): 一个配置为加载原始数据的Real3DADDataset实例。
            scale_mode (str): 点云归一化模式 ('shape_bbox', etc.)。
            num_points (int): 最终采样到的点数。
            sampling_method (str): 采样方法 ('fps', 'hybrid', etc.)。
            use_augmentation (bool): 是否在对齐时启用随机翻转等数据增强。
        """
        super().__init__()
        self.root_dir = root_dir
        self.raw_data_source = raw_data_source
        self.scale_mode = scale_mode
        self.num_points = num_points
        self.sampling_method = sampling_method
        self.use_augmentation = use_augmentation
        self.samples = []
        
        # 1. 创建从原始文件名到其在 raw_data_source 中索引的映射
        self.raw_data_map = {
            os.path.splitext(item['filename'])[0]: idx 
            for idx, item in enumerate(self.raw_data_source.pointclouds)
        }
        
        # 2. 扫描缺陷目录并构建样本列表
        print(f"从 '{root_dir}' 扫描预计算的缺陷样本...")
        template_dirs = [d for d in glob.glob(os.path.join(root_dir, '*')) if os.path.isdir(d)]
        for dir_path in template_dirs:
            template_id = os.path.basename(dir_path)
            if template_id not in self.raw_data_map:
                continue
            
            defect_files = glob.glob(os.path.join(dir_path, '*.pcd'))
            for defect_path in defect_files:
                filename = os.path.basename(defect_path)
                if filename == 'original.pcd':
                    continue

                # 修复后的标签映射：1 for bulge, 2 for sink
                if 'bulge' in filename: defect_type_idx = 1
                elif 'sink' in filename: defect_type_idx = 2
                else: continue
                
                self.samples.append({
                    'anom_path': defect_path,
                    'raw_norm_idx': self.raw_data_map[template_id], # 存储原始正常样本的索引
                    'defect_label': defect_type_idx,
                })
        
        if not self.samples:
            raise FileNotFoundError(f"在目录 {root_dir} 中没有找到任何有效的缺陷样本。")
            
        print(f"扫描完成，找到 {len(self.samples)} 个缺陷样本。")

    def __len__(self):
        return len(self.samples)

    def __getitem__(self, idx):
        sample_info = self.samples[idx]
        
        # --- 1. 加载原始数据 (高分辨率, 原始坐标) ---
        try:
            raw_anom_pcd = o3d.io.read_point_cloud(sample_info['anom_path'])
            raw_anom_points = torch.from_numpy(np.asarray(raw_anom_pcd.points, dtype=np.float32))

            raw_norm_item = self.raw_data_source[sample_info['raw_norm_idx']]
            raw_norm_points = raw_norm_item['pointcloud']
        except Exception as e:
            print(f"错误: 加载文件失败 {sample_info['anom_path']} 或索引 {sample_info['raw_norm_idx']}: {e}")
            return None # DataLoader 会自动跳过 None

        # --- 2. 统一归一化 (Scaling) ---
        shift, scale_val = torch.zeros(1, 3), torch.ones(1, 1)
        if self.scale_mode == 'shape_bbox':
            pc_max, _ = raw_norm_points.max(0, keepdim=True)
            pc_min, _ = raw_norm_points.min(0, keepdim=True)
            shift = (pc_min + pc_max) / 2
            scale_val = (pc_max - pc_min).max() / 2
        elif self.scale_mode is not None:
            print(f"警告: scale_mode '{self.scale_mode}' 在 PrecomputedDefectDataset 中未支持。")
        
        # 应用相同的变换
        norm_points_scaled = (raw_norm_points - shift) / (scale_val + 1e-8)
        anom_points_scaled = (raw_anom_points - shift) / (scale_val + 1e-8)

        # --- 3. 统一对齐 (Alignment) ---
        pair_rot = PairPCARotate(
            random_flip=self.use_augmentation,
            gravity_align=True,
        )
        pair_rot.fit(norm_points_scaled)
        norm_points_aligned = pair_rot.apply(norm_points_scaled)
        anom_points_aligned = pair_rot.apply(anom_points_scaled)
        
        # --- 4. 统一采样 (Sampling) ---
        norm_points_final = norm_points_aligned
        anom_points_final = anom_points_aligned

        if self.num_points is not None:
            # 确保采样函数可用
            if self.sampling_method == 'fps':
                norm_points_final = farthest_point_sampling(norm_points_aligned, self.num_points)
                anom_points_final = farthest_point_sampling(anom_points_aligned, self.num_points)
            elif self.sampling_method == 'hybrid':
                norm_points_final = hybrid_gps_fps_sampling(norm_points_aligned, self.num_points)
                anom_points_final = hybrid_gps_fps_sampling(anom_points_aligned, self.num_points)
            else: # Fallback to random sampling if method is not recognized
                N = norm_points_aligned.shape[0]
                if N > self.num_points:
                    indices = torch.randperm(N)[:self.num_points]
                    norm_points_final = norm_points_aligned[indices]
                
                N_anom = anom_points_aligned.shape[0]
                if N_anom > self.num_points:
                    indices_anom = torch.randperm(N_anom)[:self.num_points]
                    anom_points_final = anom_points_aligned[indices_anom]

        # --- 5. 返回最终处理好的数据 ---
        return {
            'pointcloud_anom': anom_points_final.contiguous(),
            'pointcloud_norm': norm_points_final.contiguous(),
            'defect_label': torch.tensor(sample_info['defect_label'], dtype=torch.long),
            'original_filename': raw_norm_item['filename']
        }

class ValidationDataset(Dataset):
    """
    一个专门用于验证的Dataset。
    确保验证集（只包含正常样本）的处理流程与训练集完全一致。
    处理流程: 归一化 -> 对齐 -> 采样。
    """
    def __init__(self, raw_data_source: Real3DADDataset, 
                 scale_mode: str, num_points: int, sampling_method: str):
        super().__init__()
        self.raw_data_source = raw_data_source
        self.scale_mode = scale_mode
        self.num_points = num_points
        self.sampling_method = sampling_method

    def __len__(self):
        return len(self.raw_data_source)

    def __getitem__(self, idx):
        # 1. 从源获取原始数据
        raw_item = self.raw_data_source[idx]
        raw_norm_points = raw_item['pointcloud']

        # 2. 归一化 (Scaling)
        shift, scale_val = torch.zeros(1, 3), torch.ones(1, 1)
        if self.scale_mode == 'shape_bbox':
            pc_max, _ = raw_norm_points.max(0, keepdim=True)
            pc_min, _ = raw_norm_points.min(0, keepdim=True)
            shift = (pc_min + pc_max) / 2
            scale_val = (pc_max - pc_min).max() / 2
        
        norm_points_scaled = (raw_norm_points - shift) / (scale_val + 1e-8)

        # 3. 对齐 (Alignment) - 使用非配对的PCARotate，因为只有一个输入
        # 这里的 PCARotate 应该是你之前定义的那个可以在 transforms.Compose 中使用的版本
        # 为了代码完整，我们假设它叫 `SinglePCARotate`
        # 如果你已经删了，可以用 PairPCARotate 代替，效果一样
        align_rot = PairPCARotate(random_flip=False, gravity_align=True)
        align_rot.fit(norm_points_scaled)
        norm_points_aligned = align_rot.apply(norm_points_scaled)
        
        # 4. 采样 (Sampling)
        norm_points_final = norm_points_aligned
        if self.num_points is not None:
            if self.sampling_method == 'fps':
                norm_points_final = farthest_point_sampling(norm_points_aligned, self.num_points)
            elif self.sampling_method == 'hybrid':
                norm_points_final = hybrid_gps_fps_sampling(norm_points_aligned, self.num_points)
            else: # Fallback to random
                N = norm_points_aligned.shape[0]
                if N > self.num_points:
                    indices = torch.randperm(N)[:self.num_points]
                    norm_points_final = norm_points_aligned[indices]
        
        # 5. 返回，注意key要和Real3DADDataset保持一致，方便dataloader处理
        return {
            'pointcloud': norm_points_final.contiguous(),
            'category': raw_item['category'],
            'filename': raw_item['filename'],
            # 附加上shift和scale，万一之后可视化需要反归一化
            'shift': shift.contiguous(),
            'scale': torch.full((1, 3), scale_val.item() + 1e-8).contiguous()
        }
class FastPreprocessedDataset(Dataset):
    """
    一个极速的Dataset，用于加载由 preprocess.py 生成的.pt文件。
    它不做任何计算，只负责从硬盘读取已处理好的张量。
    """
    def __init__(self, root_dir: str, transform=None):
        """
        Args:
            root_dir (str): 预处理好的.pt文件所在的根目录 (e.g., './processed_data')
            transform: 可选的、用于实时数据增强的transform (e.g., Jitter)
        """
        super().__init__()
        self.root_dir = root_dir
        self.transform = transform
        
        # 递归地查找所有.pt文件
        self.file_paths = sorted(list(glob.glob(os.path.join(root_dir, '**', '*.pt'), recursive=True)))
        
        if not self.file_paths:
            raise FileNotFoundError(f"在目录 {root_dir} 中没有找到任何预处理的 .pt 文件。")
            
        print(f"FastPreprocessedDataset 初始化完成，找到 {len(self.file_paths)} 个预处理样本。")

    def __len__(self):
        return len(self.file_paths)

    def __getitem__(self, idx):
        # 直接加载已保存的字典
        data_dict = torch.load(self.file_paths[idx])
        
        # (可选) 在这里应用一些轻量级的、实时的随机数据增强
        if self.transform:
            # 例如，你可以传入一个只包含 Jitter 的 transform
            # 注意：这里的transform需要能处理字典输入
            pass

        return data_dict
class CustomPointCloudDataset(Dataset):
    def __init__(self, path, split='train', scale=True, normalize_shape_method='shape_unit', transform=None, random_subsample=False, subsample_size=2048):
        """自定义数据集，支持与ShapeNetCore相同的归一化方法
        
        Args:
            path (str): 数据集路径
            split (str): 'train', 'val' 或 'test'
            scale (bool): 是否归一化点云
            normalize_shape_method (str): 归一化方法，'global_unit', 'shape_unit' 或 'shape_bbox'
            transform: 额外的数据变换
            random_subsample (bool): 是否随机采样点
            subsample_size (int): 采样点数
        """
        super().__init__()
        self.path = path
        self.split = split
        self.scale = scale
        self.normalize_shape_method = normalize_shape_method
        self.transform = transform
        self.random_subsample = random_subsample
        self.subsample_size = subsample_size
        
        # 根据split确定数据目录
        self.data_dir = os.path.join(path, split)
        if not os.path.exists(self.data_dir):
            raise RuntimeError(f"数据目录不存在: {self.data_dir}")
        
        # 搜索所有.npy文件
        self.files = [f for f in os.listdir(self.data_dir) if f.endswith('.npy')]
        
        # 全局归一化参数
        self.global_scale = None
        if scale and normalize_shape_method == 'global_unit':
            # 计算全局缩放因子
            all_points = []
            for file in self.files[:min(100, len(self.files))]:  # 使用部分文件以加快计算
                points = np.load(os.path.join(self.data_dir, file))
                all_points.append(points)
            
            all_points = np.vstack(all_points)
            self.global_center = np.mean(all_points, axis=0, keepdims=True)
            self.global_scale = np.max(np.abs(all_points - self.global_center))
    
    def __len__(self):
        return len(self.files)
    
    def __getitem__(self, idx):
        file_path = os.path.join(self.data_dir, self.files[idx])
        points = np.load(file_path).astype(np.float32)
        
        # 归一化
        if self.scale:
            if self.normalize_shape_method == 'global_unit':
                # 全局归一化
                if self.global_scale is not None:
                    points = (points - self.global_center) / self.global_scale * 0.5
            
            elif self.normalize_shape_method == 'shape_unit':
                # 每个形状独立归一化到单位立方体
                center = np.mean(points, axis=0, keepdims=True)
                scale = np.max(np.abs(points - center))
                points = (points - center) / (scale + 1e-8) * 0.5
            
            elif self.normalize_shape_method == 'shape_bbox':
                # 每个形状归一化到边界框
                min_vals = np.min(points, axis=0, keepdims=True)
                max_vals = np.max(points, axis=0, keepdims=True)
                points = (points - min_vals) / (max_vals - min_vals + 1e-8)
                points = points - 0.5  # 移到[-0.5, 0.5]范围
        
        # 随机采样
        if self.random_subsample and points.shape[0] > self.subsample_size:
            indices = np.random.choice(points.shape[0], self.subsample_size, replace=False)
            points = points[indices]
        
        # 转换为tensor
        points = torch.from_numpy(points).float()
        
        # 应用额外变换
        if self.transform is not None:
            points = self.transform(points)
        
        return {
            'points': points,
            'filename': self.files[idx]
        }




synsetid_to_cate = {
    '02691156': 'airplane', '02773838': 'bag', '02801938': 'basket',
    '02808440': 'bathtub', '02818832': 'bed', '02828884': 'bench',
    '02876657': 'bottle', '02880940': 'bowl', '02924116': 'bus',
    '02933112': 'cabinet', '02747177': 'can', '02942699': 'camera',
    '02954340': 'cap', '02958343': 'car', '03001627': 'chair',
    '03046257': 'clock', '03207941': 'dishwasher', '03211117': 'monitor',
    '04379243': 'table', '04401088': 'telephone', '02946921': 'tin_can',
    '04460130': 'tower', '04468005': 'train', '03085013': 'keyboard',
    '03261776': 'earphone', '03325088': 'faucet', '03337140': 'file',
    '03467517': 'guitar', '03513137': 'helmet', '03593526': 'jar',
    '03624134': 'knife', '03636649': 'lamp', '03642806': 'laptop',
    '03691459': 'speaker', '03710193': 'mailbox', '03759954': 'microphone',
    '03761084': 'microwave', '03790512': 'motorcycle', '03797390': 'mug',
    '03928116': 'piano', '03938244': 'pillow', '03948459': 'pistol',
    '03991062': 'pot', '04004475': 'printer', '04074963': 'remote_control',
    '04090263': 'rifle', '04099429': 'rocket', '04225987': 'skateboard',
    '04256520': 'sofa', '04330267': 'stove', '04530566': 'vessel',
    '04554684': 'washer', '02992529': 'cellphone',
    '02843684': 'birdhouse', '02871439': 'bookshelf',
    # '02858304': 'boat', no boat in our dataset, merged into vessels
    # '02834778': 'bicycle', not in our taxonomy
}
cate_to_synsetid = {v: k for k, v in synsetid_to_cate.items()}


class ShapeNetCore(Dataset):

    GRAVITATIONAL_AXIS = 1
    
    def __init__(self, path, cates, split, scale_mode, transform=None):
        super().__init__()
        assert isinstance(cates, list), '`cates` must be a list of cate names.'
        assert split in ('train', 'val', 'test')
        assert scale_mode is None or scale_mode in ('global_unit', 'shape_unit', 'shape_bbox', 'shape_half', 'shape_34')
        self.path = path
        if 'all' in cates:
            cates = cate_to_synsetid.keys()
        self.cate_synsetids = [cate_to_synsetid[s] for s in cates]
        self.cate_synsetids.sort()
        self.split = split
        self.scale_mode = scale_mode
        self.transform = transform

        self.pointclouds = []
        self.stats = None

        self.get_statistics()
        self.load()

    def get_statistics(self):

        basename = os.path.basename(self.path)
        dsetname = basename[:basename.rfind('.')]
        stats_dir = os.path.join(os.path.dirname(self.path), dsetname + '_stats')
        os.makedirs(stats_dir, exist_ok=True)

        if len(self.cate_synsetids) == len(cate_to_synsetid):
            stats_save_path = os.path.join(stats_dir, 'stats_all.pt')
        else:
            stats_save_path = os.path.join(stats_dir, 'stats_' + '_'.join(self.cate_synsetids) + '.pt')
        if os.path.exists(stats_save_path):
            self.stats = torch.load(stats_save_path)
            return self.stats

        with h5py.File(self.path, 'r') as f:
            pointclouds = []
            for synsetid in self.cate_synsetids:
                for split in ('train', 'val', 'test'):
                    pointclouds.append(torch.from_numpy(f[synsetid][split][...]))

        all_points = torch.cat(pointclouds, dim=0) # (B, N, 3)
        B, N, _ = all_points.size()
        mean = all_points.view(B*N, -1).mean(dim=0) # (1, 3)
        std = all_points.view(-1).std(dim=0)        # (1, )

        self.stats = {'mean': mean, 'std': std}
        torch.save(self.stats, stats_save_path)
        return self.stats

    def load(self):

        def _enumerate_pointclouds(f):
            for synsetid in self.cate_synsetids:
                cate_name = synsetid_to_cate[synsetid]
                for j, pc in enumerate(f[synsetid][self.split]):
                    yield torch.from_numpy(pc), j, cate_name
        
        with h5py.File(self.path, mode='r') as f:
            for pc, pc_id, cate_name in _enumerate_pointclouds(f):

                if self.scale_mode == 'global_unit':
                    shift = pc.mean(dim=0).reshape(1, 3)
                    scale = self.stats['std'].reshape(1, 1)
                elif self.scale_mode == 'shape_unit':
                    shift = pc.mean(dim=0).reshape(1, 3)
                    scale = pc.flatten().std().reshape(1, 1) * 2  # 乘以2使得范围在[-0.5,0.5]之间
                elif self.scale_mode == 'shape_half':
                    shift = pc.mean(dim=0).reshape(1, 3)
                    scale = pc.flatten().std().reshape(1, 1) / (0.5)
                elif self.scale_mode == 'shape_34':
                    shift = pc.mean(dim=0).reshape(1, 3)
                    scale = pc.flatten().std().reshape(1, 1) / (0.75)
                elif self.scale_mode == 'shape_bbox':
                    pc_max, _ = pc.max(dim=0, keepdim=True) # (1, 3)
                    pc_min, _ = pc.min(dim=0, keepdim=True) # (1, 3)
                    shift = ((pc_min + pc_max) / 2).view(1, 3)
                    scale = (pc_max - pc_min).max().reshape(1, 1) / 2
                else:
                    shift = torch.zeros([1, 3])
                    scale = torch.ones([1, 1])

                pc = (pc - shift) / scale

                # 不在加载时采样，而是在获取时采样，以利用缓存机制
                # 确保点云数据有效后再添加
                if pc is not None and pc.shape[1] == 3:
                    self.pointclouds.append({
                        'pointcloud': pc,
                        'category': cate_name,
                        'id': pc_id,
                        'filename': pc_id,
                        'path': pc_id,
                        'shift': shift,
                        'scale': scale,
                        'has_gt': True,
                        'gt_path': pc_id,
                        'anomaly_type': None
                    })
                else:
                    print(f"警告: 跳过无效点云 {pc_id}, 形状: {pc.shape if pc is not None else 'None'}")

        # Deterministically shuffle the dataset
        self.pointclouds.sort(key=lambda data: data['id'], reverse=False)
        random.Random(2020).shuffle(self.pointclouds)

    def __len__(self):
        return len(self.pointclouds)

    def __getitem__(self, idx):
        data = {k:v.clone() if isinstance(v, torch.Tensor) else copy(v) for k, v in self.pointclouds[idx].items()}
        if self.transform is not None:
            data = self.transform(data)
        return data

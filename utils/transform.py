# utils/transform.py
import torch
import numpy as np
import math
import random
import numbers
# (保留原有的transform.py中的其他类和导入)
from itertools import repeat


class Center(object): #保留原有代码
    r"""Centers node positions around the origin."""

    def __init__(self, attr):
        self.attr = attr

    def __call__(self, data):
        for key in self.attr:
            data[key] = data[key] - data[key].mean(dim=-2, keepdim=True)
        return data

    def __repr__(self):
        return '{}()'.format(self.__class__.__name__)


class NormalizeScale(object): #保留原有代码
    r"""Centers and normalizes node positions to the interval :math:`(-1, 1)`.
    """

    def __init__(self, attr):
        self.center = Center(attr=attr)
        self.attr = attr

    def __call__(self, data):
        data = self.center(data)

        for key in self.attr:
            scale = (1 / data[key].abs().max()) * 0.999999
            data[key] = data[key] * scale

        return data


class FixedPoints(object): #保留原有代码
    r"""Samples a fixed number of :obj:`num` points and features from a point
    cloud.
    Args:
        num (int): The number of points to sample.
        replace (bool, optional): If set to :obj:`False`, samples fixed
            points without replacement. In case :obj:`num` is greater than
            the number of points, duplicated points are kept to a
            minimum. (default: :obj:`True`)
    """

    def __init__(self, num, replace=True):
        self.num = num
        self.replace = replace
        # warnings.warn('FixedPoints is not deterministic')

    def __call__(self, data):
        num_nodes = data['pos'].size(0)
        data['dense'] = data['pos']

        if self.replace:
            choice = np.random.choice(num_nodes, self.num, replace=True)
        else:
            choice = torch.cat([
                torch.randperm(num_nodes)
                for _ in range(math.ceil(self.num / num_nodes))
            ], dim=0)[:self.num]

        for key, item in data.items():
            if torch.is_tensor(item) and item.size(0) == num_nodes and key != 'dense':
                data[key] = item[choice]

        return data

    def __repr__(self):
        return '{}({}, replace={})'.format(self.__class__.__name__, self.num,
                                           self.replace)


class LinearTransformation(object): #保留原有代码
    r"""Transforms node positions with a square transformation matrix computed
    offline.
    Args:
        matrix (Tensor): tensor with shape :math:`[D, D]` where :math:`D`
            corresponds to the dimensionality of node positions.
    """

    def __init__(self, matrix, attr):
        assert matrix.dim() == 2, (
            'Transformation matrix should be two-dimensional.')
        assert matrix.size(0) == matrix.size(1), (
            'Transformation matrix should be square. Got [{} x {}] rectangular'
            'matrix.'.format(*matrix.size()))

        self.matrix = matrix
        self.attr = attr

    def __call__(self, data):
        for key in self.attr:
            pos = data[key].view(-1, 1) if data[key].dim() == 1 else data[key]

            assert pos.size(-1) == self.matrix.size(-2), (
                'Node position matrix and transformation matrix have incompatible '
                'shape.')

            data[key] = torch.matmul(pos, self.matrix.to(pos.dtype).to(pos.device))

        return data

    def __repr__(self):
        return '{}({})'.format(self.__class__.__name__, self.matrix.tolist())


class RandomRotate(object): #保留原有代码
    r"""Rotates node positions around a specific axis by a randomly sampled
    factor within a given interval.
    Args:
        degrees (tuple or float): Rotation interval from which the rotation
            angle is sampled. If :obj:`degrees` is a number instead of a
            tuple, the interval is given by :math:`[-\mathrm{degrees},
            \mathrm{degrees}]`.
        axis (int, optional): The rotation axis. (default: :obj:`0`)
    """

    def __init__(self, degrees, attr, axis=0):
        if isinstance(degrees, numbers.Number):
            degrees = (-abs(degrees), abs(degrees))
        assert isinstance(degrees, (tuple, list)) and len(degrees) == 2
        self.degrees = degrees
        self.axis = axis
        self.attr = attr

    def __call__(self, data):
        degree = math.pi * random.uniform(*self.degrees) / 180.0
        sin, cos = math.sin(degree), math.cos(degree)

        if self.axis == 0:
            matrix = [[1, 0, 0], [0, cos, sin], [0, -sin, cos]]
        elif self.axis == 1:
            matrix = [[cos, 0, -sin], [0, 1, 0], [sin, 0, cos]]
        else:
            matrix = [[cos, sin, 0], [-sin, cos, 0], [0, 0, 1]]
        return LinearTransformation(torch.tensor(matrix), attr=self.attr)(data)

    def __repr__(self):
        return '{}({}, axis={})'.format(self.__class__.__name__, self.degrees,
                                        self.axis)

# <<<<<< 新增 PCARotate 类 >>>>>>
class PCARotate(object):
    r"""Rotates a point cloud based on its principal components.
    The point cloud is first centered, then rotated so its principal axes align with the coordinate axes.
    """
    def __init__(self, attr_keys=['pointcloud', 'pos'], random_flip=True, always_align_y_to_gravity=False, gravity_axis=1):
        """
        Args:
            attr_keys (list of str): Keys in the data dictionary to apply the PCA rotation to.
                                     These should correspond to point cloud tensors of shape (N, 3).
            random_flip (bool): If True, randomly flips the direction of principal axes to augment data.
                                This helps in disambiguating axis directions (e.g. +x vs -x).
            always_align_y_to_gravity (bool): If True, and if a dominant vertical axis can be identified
                                             (e.g., for objects like chairs, tables), it attempts to align
                                             this axis with the specified gravity_axis (usually Y).
            gravity_axis (int): Index of the axis to be considered as 'up' (0 for X, 1 for Y, 2 for Z).
                                Used if always_align_y_to_gravity is True.
        """
        self.attr_keys = attr_keys
        self.random_flip = random_flip
        self.always_align_y_to_gravity = always_align_y_to_gravity
        self.gravity_axis = gravity_axis


    def __call__(self, data):
        for key in self.attr_keys:
            if key not in data or not isinstance(data[key], torch.Tensor) or data[key].ndim < 2 or data[key].shape[-1] != 3:
                # print(f"Skipping PCA for key '{key}': Not found, not a tensor, or not 3D points.")
                continue

            points = data[key].clone() # (N, 3)
            if points.shape[0] < 3: # PCA needs at least as many points as dimensions for non-degenerate covariance
                # print(f"Skipping PCA for key '{key}': Not enough points ({points.shape[0]}) for PCA.")
                continue

            # 1. Center the point cloud
            mean = torch.mean(points, dim=0, keepdim=True)
            centered_points = points - mean

            # 2. Compute covariance matrix
            # Ensure centered_points is float64 for higher precision in covariance calculation if needed,
            # but float32 is usually fine for torch.
            # H = torch.matmul(centered_points.T, centered_points) / (points.shape[0] -1) # (3,3)
            # For torch.linalg.eig or svd, input should be (..., D, D)
            # A more stable way for small number of points might be to use SVD on centered_points
            # U, S, V = torch.linalg.svd(centered_points)
            # components = V # Principal components are rows of V (or columns of V.T)

            # Using covariance matrix directly:
            # For PyTorch versions < 1.9, torch.cov is not available.
            # Manual calculation:
            if centered_points.is_cuda: # torch.cov might not be available or efficient on older cuda versions for small matrices
                # Fallback to numpy for covariance if torch.cov is problematic or for older versions
                # However, this involves CPU-GPU transfer. Let's try to keep it in torch.
                if hasattr(torch, 'cov'):
                     # Correct way to get covariance for (N,D) -> (D,D)
                    if points.shape[0] == 1: # Cannot compute covariance for a single point
                        covariance_matrix = torch.eye(3, device=points.device, dtype=points.dtype) # Identity, no rotation
                    else:
                        covariance_matrix = torch.cov(centered_points.T)
                else: # Manual for older PyTorch or if torch.cov causes issues
                    # This is actually outer product sum, not covariance.
                    # covariance_matrix = torch.matmul(centered_points.T, centered_points) / max(1, points.shape[0] -1)
                    # For (N, D) input, covariance is (D, D). centered_points.T is (D, N)
                    # Correct manual covariance:
                    num_pts = centered_points.shape[0]
                    if num_pts <=1:
                        covariance_matrix = torch.eye(3, device=points.device, dtype=points.dtype)
                    else:
                        covariance_matrix = (centered_points.T @ centered_points) / (num_pts - 1)

            else: # CPU calculation
                if hasattr(torch, 'cov'):
                    if points.shape[0] == 1:
                        covariance_matrix = torch.eye(3, device=points.device, dtype=points.dtype)
                    else:
                        covariance_matrix = torch.cov(centered_points.T) # PyTorch 1.9+
                else:
                    # For older PyTorch, you might need to use numpy or implement manually
                    # For simplicity, let's assume torch.cov is available or use a manual version
                    # np_centered_points = centered_points.cpu().numpy()
                    # np_covariance_matrix = np.cov(np_centered_points, rowvar=False)
                    # covariance_matrix = torch.from_numpy(np_covariance_matrix).to(points.device).to(points.dtype)
                    num_pts = centered_points.shape[0]
                    if num_pts <=1:
                        covariance_matrix = torch.eye(3, device=points.device, dtype=points.dtype)
                    else:
                        covariance_matrix = (centered_points.T @ centered_points) / (num_pts - 1)


            # 3. Eigen decomposition
            try:
                # Eigenvalues are sorted in ascending order by torch.linalg.eigh
                eigenvalues, eigenvectors = torch.linalg.eigh(covariance_matrix)
                # We want principal components corresponding to largest eigenvalues, so reverse order
                # eigenvectors are columns: eigenvectors[:, i] is the i-th eigenvector
                # Let R be the matrix whose columns are the eigenvectors v1, v2, v3 (sorted by eigenvalue descending)
                # R = [v_largest, v_middle, v_smallest]
                rotation_matrix = torch.flip(eigenvectors, dims=[1]) # Flip to get descending order of eigenvalues
            except Exception as e:
                # print(f"PCA Eigen decomposition failed for key '{key}': {e}. Using identity rotation.")
                rotation_matrix = torch.eye(3, device=points.device, dtype=points.dtype)


            # Ensure a right-handed coordinate system
            # If det(R) is -1, flip one axis (e.g., the one corr. to smallest eigenvalue)
            if torch.linalg.det(rotation_matrix) < 0:
                rotation_matrix[:, -1] *= -1 # Flip the last column (corr. to smallest eigenval after flip)

            # Optional: Randomly flip axes directions for augmentation

            if self.random_flip:
                # 创建一个对角矩阵，对角线元素随机为 +1 或 -1
                signs = torch.randint(0, 2, (3,), device=points.device, dtype=points.dtype) * 2 - 1
                # signs.diag() 创建对角矩阵
                flip_matrix = torch.diag(signs)
                # 将翻转应用到旋转矩阵
                rotation_matrix = rotation_matrix @ flip_matrix

                # 再次检查并确保是右手坐标系
                if torch.linalg.det(rotation_matrix) < 0:
                    rotation_matrix[:, -1] *= -1 # 翻转贡献最小的轴

            # Optional: Align a dominant "gravity" axis (e.g., for chairs)
            # This is a heuristic and might not work for all object types.
            if self.always_align_y_to_gravity:
                # Find which principal component aligns most with the world's gravity axis (e.g., world Y [0,1,0])
                world_gravity = torch.zeros(3, device=points.device, dtype=points.dtype)
                world_gravity[self.gravity_axis] = 1.0

                # Current principal axes (columns of rotation_matrix)
                pc_axis_x = rotation_matrix[:, 0]
                pc_axis_y = rotation_matrix[:, 1]
                pc_axis_z = rotation_matrix[:, 2]

                # Dot products with world gravity
                dot_x = torch.abs(torch.dot(pc_axis_x, world_gravity))
                dot_y = torch.abs(torch.dot(pc_axis_y, world_gravity))
                dot_z = torch.abs(torch.dot(pc_axis_z, world_gravity))

                # Identify which PC axis is most vertical
                dots = torch.tensor([dot_x, dot_y, dot_z])
                most_vertical_pc_idx = torch.argmax(dots)

                # Target: align this most_vertical_pc_idx with the self.gravity_axis (e.g., Y)
                # And ensure it points "up" (positive direction of gravity_axis)
                # This is a more complex alignment. A simpler PCA just aligns to axes.
                # For now, this part is a placeholder for a more robust "up" alignment logic.
                # A full solution might involve creating a new rotation matrix.
                # For example, if pc_axis_z is most vertical, we want to rotate such that
                # pc_axis_z becomes the new Y, and pc_axis_x, pc_axis_y form the new XZ plane.
                # This might conflict with pure PCA alignment if PCA's primary axis isn't vertical.

                # For now, let's just ensure the chosen most_vertical_pc_idx (if it's meant to be the new Y)
                # points towards positive Y.
                # This simple flip might not be a full gravity alignment but can help standardize "up".
                if rotation_matrix[self.gravity_axis, most_vertical_pc_idx] < 0: # If the Y component of the most vertical PC is negative
                    rotation_matrix[:, most_vertical_pc_idx] *= -1
                
                # Re-ensure right-handed system after potential flip
                if torch.linalg.det(rotation_matrix) < 0:
                    # Find an axis to flip that is NOT the most_vertical_pc_idx, preferably the one least aligned with gravity
                    axes_to_flip = [i for i in range(3) if i != most_vertical_pc_idx]
                    if axes_to_flip: # Should always be true
                        axis_to_flip_idx = axes_to_flip[torch.argmin(dots[axes_to_flip])]
                        rotation_matrix[:, axis_to_flip_idx] *= -1


            # 4. Apply rotation: P_rotated = P_centered @ R
            # Note: if eigenvectors are columns of R, then to transform points p to the new coord system
            # defined by R, it's p_new = R^T @ p_centered_column_vector
            # Or for row vectors: p_new_row = p_centered_row @ R
            rotated_points = torch.matmul(centered_points, rotation_matrix)

            # 5. Add back the original mean (optional, but usually registration implies canonical pose at origin)
            # For canonical pose, we usually want it centered.
            # data[key] = rotated_points + mean
            data[key] = rotated_points # Keep it centered

        return data

    def __repr__(self):
        return f'{self.__class__.__name__}(attr_keys={self.attr_keys}, random_flip={self.random_flip}, always_align_y_to_gravity={self.always_align_y_to_gravity}, gravity_axis={self.gravity_axis})'


class AddNoise(object): #保留原有代码

    def __init__(self, std=0.01, noiseless_item_key='clean'):
        self.std = std
        self.key = noiseless_item_key

    def __call__(self, data):
        data[self.key] = data['pos']
        data['pos'] = data['pos'] + torch.normal(mean=0, std=self.std, size=data['pos'].size())
        return data


class AddRandomNoise(object): #保留原有代码

    def __init__(self, std_range=[0, 0.10], noiseless_item_key='clean'):
        self.std_range = std_range
        self.key = noiseless_item_key

    def __call__(self, data):
        noise_std = random.uniform(*self.std_range)
        data[self.key] = data['pos']
        data['pos'] = data['pos'] + torch.normal(mean=0, std=noise_std, size=data['pos'].size())
        return data


class AddNoiseForEval(object): #保留原有代码

    def __init__(self, stds=[0.0, 0.01, 0.02, 0.03, 0.05, 0.10, 0.15]):
        self.stds = stds
        self.keys = ['noisy_%.2f' % s for s in stds]

    def __call__(self, data):
        data['clean'] = data['pos']
        for noise_std in self.stds:
            data['noisy_%.2f' % noise_std] = data['pos'] + torch.normal(mean=0, std=noise_std, size=data['pos'].size())
        return data


class IdentityTransform(object): #保留原有代码
    
    def __call__(self, data):
        return data


class RandomScale(object): #保留原有代码
    r"""Scales node positions by a randomly sampled factor :math:`s` within a
    given interval, *e.g.*, resulting in the transformation matrix
    .. math::
        \begin{bmatrix}
            s & 0 & 0 \\
            0 & s & 0 \\
            0 & 0 & s \\
        \end{bmatrix}
    for three-dimensional positions.
    Args:
        scales (tuple): scaling factor interval, e.g. :obj:`(a, b)`, then scale
            is randomly sampled from the range
            :math:`a \leq \mathrm{scale} \leq b`.
    """

    def __init__(self, scales, attr):
        assert isinstance(scales, (tuple, list)) and len(scales) == 2
        self.scales = scales
        self.attr = attr

    def __call__(self, data):
        scale = random.uniform(*self.scales)
        for key in self.attr:
            data[key] = data[key] * scale
        return data

    def __repr__(self):
        return '{}({})'.format(self.__class__.__name__, self.scales)


class RandomTranslate(object): #保留原有代码
    r"""Translates node positions by randomly sampled translation values
    within a given interval. In contrast to other random transformations,
    translation is applied separately at each position.
    Args:
        translate (sequence or float or int): Maximum translation in each
            dimension, defining the range
            :math:`(-\mathrm{translate}, +\mathrm{translate})` to sample from.
            If :obj:`translate` is a number instead of a sequence, the same
            range is used for each dimension.
    """

    def __init__(self, translate, attr):
        self.translate = translate
        self.attr = attr

    def __call__(self, data):
        (n, dim), t = data['pos'].size(), self.translate
        if isinstance(t, numbers.Number):
            t = list(repeat(t, times=dim))
        assert len(t) == dim

        ts = []
        for d in range(dim):
            ts.append(data['pos'].new_empty(n).uniform_(-abs(t[d]), abs(t[d])))

        for key in self.attr:
            data[key] = data[key] + torch.stack(ts, dim=-1)

        return data

    def __repr__(self):
        return '{}({})'.format(self.__class__.__name__, self.translate)


class Rotate(object): #保留原有代码
    r"""Rotates node positions around a specific axis by a randomly sampled
    factor within a given interval.
    Args:
        degrees (tuple or float): Rotation interval from which the rotation
            angle is sampled. If :obj:`degrees` is a number instead of a
            tuple, the interval is given by :math:`[-\mathrm{degrees},
            \mathrm{degrees}]`.
        axis (int, optional): The rotation axis. (default: :obj:`0`)
    """

    def __init__(self, degree, attr, axis=0):
        self.degree = degree
        self.axis = axis
        self.attr = attr

    def __call__(self, data):
        degree = math.pi * self.degree / 180.0
        sin, cos = math.sin(degree), math.cos(degree)

        if self.axis == 0:
            matrix = [[1, 0, 0], [0, cos, sin], [0, -sin, cos]]
        elif self.axis == 1:
            matrix = [[cos, 0, -sin], [0, 1, 0], [sin, 0, cos]]
        else:
            matrix = [[cos, sin, 0], [-sin, cos, 0], [0, 0, 1]]
        return LinearTransformation(torch.tensor(matrix), attr=self.attr)(data)

    def __repr__(self):
        return '{}({}, axis={})'.format(self.__class__.__name__, self.degree, # Should be self.degree
                                        self.axis)
    

class Jitter(object):
    """
    Applies random jitter (Gaussian noise) to the positions of a point cloud.

    Args:
        sigma (float): Standard deviation of the Gaussian noise.
        clip (float, optional): Maximum absolute value for the noise. 
                                Noise values larger than this will be clipped.
        attr_keys (list of str): Keys to apply the jitter to.
    """
    def __init__(self, sigma=0.01, clip=0.05, attr_keys=['pointcloud', 'pos']):
        self.sigma = sigma
        self.clip = clip
        self.attr_keys = attr_keys

    def __call__(self, data):
        for key in self.attr_keys:
            if key in data:
                points = data[key]
                noise = torch.randn_like(points) * self.sigma
                if self.clip is not None:
                    noise = torch.clamp(noise, -self.clip, self.clip)
                data[key] = points + noise
        return data

    def __repr__(self):
        return f'{self.__class__.__name__}(sigma={self.sigma}, clip={self.clip})'
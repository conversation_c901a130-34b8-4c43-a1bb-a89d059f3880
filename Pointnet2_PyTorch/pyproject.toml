[tool.isort]
skip_glob = ["*/deps/*", "*/build/*", "*/obselete/*"]
known_third_party = ["h5py", "hydra", "lmdb", "msgpack_numpy", "numpy", "omegaconf", "pointnet2_ops", "pytest", "pytorch_lightning", "setuptools", "torch", "torchvision", "tqdm"]
multi_line_output = 3
force_grid_wrap = false
line_length = 88
include_trailing_comma = true
use_parentheses = true

[tool.black]
exclude = '''
(
  /(
      \.eggs         # exclude a few common directories in the
    | \.git          # root of the project
    | \.hg
    | \.mypy_cache
    | \.tox
    | \.venv
    | _build
    | buck-out
    | build
    | dist
    | obselete
    | deps
  )/
)
'''

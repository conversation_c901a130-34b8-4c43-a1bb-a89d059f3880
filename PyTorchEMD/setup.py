from setuptools import setup
from torch.utils.cpp_extension import BuildExtension, CUDAExtension

setup(
    name="emd_ext",
    py_modules=["emd"],               # 把 emd.py 一并安装
    ext_modules=[
        CUDAExtension(
            name="emd_cuda",          # 生成 emd_cuda.so
            sources=[
                "cuda/emd.cpp",       # ← 位于 cuda 子目录
                "cuda/emd_kernel.cu"  # ← 同上
            ],
            extra_compile_args={
                "cxx": ["-O3"],
                "nvcc": ["-O2"]
            },
        ),
    ],
    cmdclass={"build_ext": BuildExtension},
)
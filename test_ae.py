#!/usr/bin/env python3
import os
import time
import argparse
import torch
import numpy as np
import open3d as o3d
from tqdm.auto import tqdm
from torchvision import transforms

# --- 导入您项目中的模块 ---
# (请确保这些路径相对于您运行脚本的位置是正确的)
from utils.dataset import Real3DADDataset
from utils.misc import str_list, get_logger, seed_all
from utils.data import custom_collate_fn
from models.autoencoder import AutoEncoder
from utils.transform import PCARotate
from torch.utils.data import DataLoader


def main():
    """
    主函数，用于加载模型并生成特定类型的异常点云。
    """
    parser = argparse.ArgumentParser(description="使用训练好的条件扩散模型生成特定异常。")

    # --- 核心参数 ---
    parser.add_argument('--ckpt', type=str, default='/home/<USER>/llm/3DAD/diffusion-point-cloud/logs_ae_vae/AE_VAE_2025_07_28__19_31_30/model_best.pt', help='训练好的AutoEncoder模型检查点路径 (.pt)。')
    parser.add_argument('--save_dir', type=str, default='./generated_anomalies', help='保存生成点云的目录。')
    parser.add_argument('--device', type=str, default='cuda', choices=['cuda', 'cpu'], help='用于生成的设备。')

    # --- 数据源参数 ---
    parser.add_argument('--dataset_path', type=str, default='/home/<USER>/llm/3DAD/data/Real3D-AD-PCD', help='用于提供正常点云样本的数据集路径。')
    parser.add_argument('--categories', type=str_list, default=['airplane'], help='作为生成基础的物体类别列表。')
    parser.add_argument('--num_samples', type=int, default=5, help='要用于生成异常的正常样本数量。-1代表使用数据集中所有样本。')
    parser.add_argument('--batch_size', type=int, default=4, help='生成时的批次大小。')

    # --- 异常生成控制参数 ---
    parser.add_argument('--defect_type', type=str, default='sink', choices=['sink', 'bulge'], help='要生成的缺陷类型。')
    parser.add_argument('--sample_steps', type=int, default=None, help='用于加速采样的扩散步数 (None代表完整步数)。')
    
    # --- 预处理参数 (必须与训练时一致) ---
    parser.add_argument('--pca_align', type=eval, default=True, choices=[True, False], help='是否应用PCA对齐。必须与训练时的验证集设置相同。')
    
    args = parser.parse_args()

    # --- 初始化 ---
    seed_all(2025) # 使用固定种子以保证可复现性
    run_name = f"{args.defect_type}_{time.strftime('%Y%m%d_%H%M%S')}"
    output_dir = os.path.join(args.save_dir, run_name)
    os.makedirs(output_dir, exist_ok=True)
    logger = get_logger('anomaly_generator', output_dir)
    logger.info(f"运行参数: {args}")

    # --- 加载模型 ---
    logger.info(f"从检查点加载模型: {args.ckpt}")
    try:
        ckpt = torch.load(args.ckpt, map_location=args.device, weights_only=False)
    except Exception as e:
        logger.error(f"加载检查点失败: {e}")
        return
        
    if 'args' not in ckpt:
        logger.error("检查点中不包含 'args'。无法构建模型。")
        return
    model_args = ckpt['args']
    
    # 使用训练时的参数来构建模型
    model = AutoEncoder(model_args).to(args.device)
    model.load_state_dict(ckpt['state_dict'])
    model.eval()
    logger.info("模型加载成功。")

    # --- 准备正常形状的数据集 ---
    eval_transform = None
    if args.pca_align:
        logger.info("对输入数据应用PCA对齐。")
        eval_transform = transforms.Compose([
            PCARotate(attr_keys=['pointcloud'], random_flip=False, always_align_y_to_gravity=True)
        ])
    
    # 加载作为“基础”的正常点云
    base_dset = Real3DADDataset(
        path=args.dataset_path,
        categories=args.categories,
        split='train', # 从训练集中选择好的形状
        scale_mode=model_args.scale_mode, # 使用训练时的归一化模式
        transform=eval_transform,
        num_points=model_args.num_points, # 使用训练时的点数
        sampling_method=model_args.sampling_method
    )
    
    if len(base_dset) == 0:
        logger.error("基础数据集为空，请检查路径和类别设置。")
        return

    loader = DataLoader(
        base_dset, 
        batch_size=args.batch_size,
        shuffle=True, # 打乱以获取随机的正常样本子集
        num_workers=2, 
        collate_fn=custom_collate_fn
    )

    # --- 生成循环 ---
    # 这个映射必须与您训练时的设定一致
    # 假设 0 是 bulge, 1 是 sink
    defect_map = {'bulge': 0, 'sink': 1} 
    defect_id = defect_map[args.defect_type]
    
    num_generated = 0
    all_generated_files = [] # 用于之后的可视化

    with torch.no_grad():
        pbar = tqdm(loader, desc=f"正在生成 '{args.defect_type}' 异常")
        for i, batch in enumerate(pbar):
            if args.num_samples != -1 and num_generated >= args.num_samples:
                break
            if batch is None: continue

            # 1. 获取一个批次的正常点云及其元数据
            pc_norm = batch['pointcloud'].to(args.device)
            B = pc_norm.size(0)
            
            # 2. 将正常点云编码为潜向量 `z`
            latent_z, _ = model.encoder(pc_norm)

            # 3. 创建与批次大小匹配的缺陷标签，并构建上下文向量
            defect_label_idx = torch.tensor([defect_id] * B, device=args.device)
            label_vec = model.label_emb(defect_label_idx)
            context = torch.cat([latent_z, label_vec], dim=1)

            # 4. 使用Diffusion解码器生成异常点云
            pc_anom_norm = model.decode(context, pc_norm.size(1), sample_steps=args.sample_steps).cpu()
            
            # 5. 将点云反归一化到原始尺度，以便保存和可视化
            shift = batch.get('shift').to(args.device)
            scale = batch.get('scale').to(args.device)
            pc_norm_orig = (pc_norm * scale + shift).cpu()
            pc_anom_orig = (pc_anom_norm.to(args.device) * scale + shift).cpu()

            # 6. 逐个保存批次中的点云为 .pcd 文件
            for j in range(B):
                if args.num_samples != -1 and num_generated >= args.num_samples:
                    break
                
                filename_base = os.path.splitext(batch['filename'][j])[0]
                
                # 保存原始点云 (用于对比)
                pcd_orig_path = os.path.join(output_dir, f"{filename_base}_original.pcd")
                pcd_orig = o3d.geometry.PointCloud()
                pcd_orig.points = o3d.utility.Vector3dVector(pc_norm_orig[j].numpy())
                pcd_orig.paint_uniform_color([0.5, 0.5, 0.5]) # 灰色
                o3d.io.write_point_cloud(pcd_orig_path, pcd_orig)

                # 保存生成的异常点云
                pcd_anom_path = os.path.join(output_dir, f"{filename_base}_generated_{args.defect_type}.pcd")
                pcd_anom = o3d.geometry.PointCloud()
                pcd_anom.points = o3d.utility.Vector3dVector(pc_anom_orig[j].numpy())
                pcd_anom.paint_uniform_color([1.0, 0.0, 0.0]) # 红色
                o3d.io.write_point_cloud(pcd_anom_path, pcd_anom)
                
                if i == 0 and j == 0: # 保存第一个样本的路径用于可视化
                    all_generated_files.extend([pcd_orig_path, pcd_anom_path])

                num_generated += 1

    logger.info(f"成功生成并保存了 {num_generated} 个异常样本到: {output_dir}")
    
    # --- 可选的可视化步骤 ---
    if not all_generated_files:
        logger.warning("没有生成任何文件，跳过可视化。")
        return

    print("\n正在显示第一个生成的‘原始点云’(灰色)和‘异常点云’(红色)。")
    print("关闭可视化窗口以退出脚本。")
    
    try:
        pcd_orig_vis = o3d.io.read_point_cloud(all_generated_files[0])
        pcd_anom_vis = o3d.io.read_point_cloud(all_generated_files[1])
        
        # 将异常点云在x轴上平移一点，方便对比
        bbox = pcd_orig_vis.get_axis_aligned_bounding_box()
        x_extent = bbox.get_extent()[0]
        pcd_anom_vis.translate((x_extent * 1.2, 0, 0))
        
        o3d.visualization.draw_geometries([pcd_orig_vis, pcd_anom_vis])
    except Exception as e:
        logger.error(f"无法打开可视化窗口: {e}")
        logger.info("请手动检查保存在以下目录中的PCD文件: " + output_dir)


if __name__ == '__main__':
    main()
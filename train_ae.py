import os
import argparse
import torch
import torch.utils.tensorboard
from torch.nn.utils import clip_grad_norm_
from tqdm.auto import tqdm
import numpy as np
import open3d as o3d # Added for PCD saving
from evaluation.evaluation_metrics import distChamfer
from emd import earth_mover_distance
from utils.dataset import *
from utils.misc import *
from utils.data import *
from utils.transform import *
from utils.ema import EMAWrapper
from models.autoencoder import *
from evaluation import EMD_CD
from torch.optim.lr_scheduler import LinearLR, CosineAnnealingLR, SequentialLR

THOUSAND = 1000

# Arguments
parser = argparse.ArgumentParser()

# --- 模型参数 ---
parser.add_argument('--latent_dim', type=int, default=256, help='Dimension of the latent space.')
parser.add_argument('--model_type', type=str, default='vae', choices=['ae', 'vae'], help='ae or vae architecture')
parser.add_argument('--resume', type=str, default=None, help='Path to checkpoint to resume training from.')

# --- VAE 相关参数 ---
parser.add_argument('--use_vae', type=eval, default=True, choices=[True, False], help='deprecated, ignored')
parser.add_argument('--kl_weight', type=float, default=0.001, help='Weight for the KL divergence term in VAE loss')

# --- 扩散解码器 (内部DiffusionPoint) 相关参数 ---
parser.add_argument('--num_steps', type=int, default=1000, help='Number of diffusion steps (T) for the decoder.')
parser.add_argument('--beta_1', type=float, default=1e-4, help='Initial beta value for variance schedule of the decoder.')
parser.add_argument('--beta_T', type=float, default=0.02, help='Final beta value for variance schedule of the decoder.')
parser.add_argument('--sched_mode', type=str, default='cosine', help='Type of variance schedule for the diffusion decoder (linear or cosine).')
parser.add_argument('--learn_sigma_diffusion', type=eval, default=True, choices=[True, False], help='Whether the diffusion decoder learns its variance.')
parser.add_argument('--residual_within_block_diffusion', type=eval, default=True, choices=[True, False], help='Whether PointwiseNet attention blocks in decoder use internal residual connections.')
parser.add_argument('--time_emb_dim_diffusion', type=int, default=128, help='Dimension of the sinusoidal time embedding in the diffusion decoder PointwiseNet.')
parser.add_argument('--lambda_vlb_diffusion', type=float, default=1e-3, help='Weight of the KL/VLB term in diffusion loss.')

# --- 数据集和加载器参数 ---
parser.add_argument('--dataset_path', type=str, default='/home/<USER>/llm/3DAD/data/Real3D-AD-PCD', help='Path to the dataset.')
parser.add_argument('--categories', type=str_list, default=['airplane'], help='List of categories to use.')
parser.add_argument('--scale_mode', type=str, default='shape_bbox', help='Point cloud normalization mode.')
parser.add_argument('--train_batch_size', type=int, default=4, help='Batch size for training.')
parser.add_argument('--val_batch_size', type=int, default=4, help='Batch size for validation.')
parser.add_argument('--num_points', type=int, default=2048, help='Number of points per point cloud.')
parser.add_argument('--sampling_method', type=str, default='hybrid', help='Point cloud sampling method.')
parser.add_argument('--rotate', type=eval, default=False, choices=[True, False], help='Whether to apply random rotation augmentation.')
parser.add_argument('--precomputed_defect_path', type=str, default="./defect_generations_strong", help='预计算缺陷样本的路径 (例如 ./defect_generations_strong)。')
# --- 优化器和调度器参数 ---
parser.add_argument('--lr', type=float, default=1e-4, help='Learning rate.') # VAEs/Diffusion might benefit from smaller LRs
parser.add_argument('--weight_decay', type=float, default=0.0, help='Weight decay for the optimizer.')
parser.add_argument('--max_grad_norm', type=float, default=1.0, help='Maximum gradient norm for clipping.')

# --- EMA 相关参数 ---
parser.add_argument('--use_ema', type=eval, default=True, choices=[True, False], help='Whether to use EMA for model weights.')
parser.add_argument('--ema_decay', type=float, default=0.9999, help='EMA decay rate. Typical values: 0.9999 for stable training, 0.999 for faster adaptation.')

# --- 训练参数 ---
parser.add_argument('--seed', type=int, default=2024, help='Random seed for reproducibility.')
parser.add_argument('--logging', type=eval, default=True, choices=[True, False], help='Enable or disable logging.')
parser.add_argument('--log_root', type=str, default='./logs_ae_vae', help='Root directory for logs.')
parser.add_argument('--device', type=str, default='cuda', help='Device to use for training (e.g., "cuda", "cpu").')
parser.add_argument('--max_iters', type=int, default=50000, help='Maximum number of training iterations.') # Increased iterations
parser.add_argument('--val_freq', type=int, default=2000, help='Frequency of validation (in iterations).')
parser.add_argument('--tag', type=str, default=None, help='Optional tag for the experiment log directory.')
parser.add_argument('--num_workers', type=int, default=4, help='Number of workers for DataLoader.')
parser.add_argument('--warmup_iters', type=int, default=2500, help='Number of linear warmup iterations.')

# --- 验证/检测/采样 参数 ---
parser.add_argument('--num_val_batches', type=int, default=4, help='Number of batches to use for validation loss calculation.') # Increased for more stable val
parser.add_argument('--num_inspect_batches', type=int, default=1, help='Number of batches for detailed inspection/visualization.')
parser.add_argument('--num_inspect_pointclouds', type=int, default=2, help='Number of point clouds to visualize during inspection.')
parser.add_argument('--ret_traj', type=eval, default=False, choices=[True, False], help='Whether to return the full trajectory during decoding/sampling.')
parser.add_argument('--sample_steps_decode', type=int, default=None, help='Number of steps for faster decoding sampler (if None, use full diffusion steps). Used in model.decode.')
parser.add_argument('--flexibility', type=float, default=0.0, help='Flexibility for sampling (passed to diffusion.sample if used, currently not directly used by improved_ddpm sample interface)')
parser.add_argument('--metrics_in_normalized_space', type=eval, default=True, choices=[True, False],
                    help='Whether to compute CD/EMD metrics in normalized space (True) or original space (False). Normalized space avoids scale inconsistency.')

# --- 预训练 VAE ---
parser.add_argument('--pretrained_vae_path', type=str, default="/home/<USER>/llm/3DAD/diffusion-point-cloud/logs_vae/VAE_2025_07_28__13_32_48/model_best.pt", help='Path to pretrained VAE checkpoint (encoder weights will be loaded)')

# Freeze encoder after loading pretrained VAE
parser.add_argument('--freeze_encoder_after', type=eval, default=True, choices=[True, False], help='Freeze encoder parameters after loading pretrained weights.')

# --- 缺陷类型及标签嵌入 ---
parser.add_argument('--num_defect_types', type=int, default=2, help='Number of defect categories including normal.')
parser.add_argument('--label_emb_dim', type=int, default=8, help='Embedding dimension for defect type label.')

args = parser.parse_args()
seed_all(args.seed)

# determine if using VAE based on model_type or legacy flag
use_vae_flag = (args.model_type.lower() == 'vae')

# Logging
log_dir = None
if args.logging:
    log_dir = get_new_log_dir(args.log_root, prefix='AE_VAE_', postfix='_' + args.tag if args.tag is not None else '')
    logger = get_logger('train', log_dir)
    writer = torch.utils.tensorboard.SummaryWriter(log_dir)
    ckpt_mgr = CheckpointManager(log_dir, max_to_keep=5) # 保存最近5个和最佳模型
    log_hyperparams(writer, args) # Log hyperparameters to TensorBoard
else:
    logger = get_logger('train', None) # Still get a logger for console output
    writer = BlackHole()
    ckpt_mgr = BlackHole()
logger.info(args)

# Datasets and loaders


pca_align = PCARotate(
    attr_keys=['pointcloud'],  # 告诉它在哪个字段找点云
    random_flip=False,         # 训练时 True；可视化/验证时 False
    always_align_y_to_gravity=True
)
train_transform = transforms.Compose([
    pca_align
    # 其它想要的随机旋转、抖动、缩放等 augmentation
])
logger.info('Loading datasets...')

original_dset = Real3DADDataset(
    path=args.dataset_path,
    categories=args.categories,
    split='train',
    scale_mode=args.scale_mode,
    transform=train_transform, # 通常在预计算数据上不需要额外变换
    num_points=args.num_points,
    sampling_method=args.sampling_method
)

# 2. 使用新的Dataset类加载预计算的缺陷数据
train_dset = PrecomputedDefectDataset(
    root_dir=args.precomputed_defect_path,
    original_dataset=original_dset,
    transform = train_transform
)

# 验证集仍然使用原始的正常数据
val_dset = original_dset 

logger.info(f"训练集 (从预计算目录加载): {len(train_dset)} 个样本")
logger.info(f"验证集 (原始正常数据): {len(val_dset)} 个样本")

train_loader = DataLoader(
    train_dset,
    batch_size=args.train_batch_size,
    shuffle=True,
    num_workers=args.num_workers,
    pin_memory=True,
    collate_fn=custom_collate_fn
)
train_iter = get_data_iterator(train_loader)

val_loader = DataLoader(
    val_dset,
    batch_size=args.val_batch_size,
    shuffle=False,
    num_workers=args.num_workers,
    collate_fn=custom_collate_fn
)



# Model
logger.info('Building model...')
# Pass all args to AutoEncoder, it will pick what it needs
model = AutoEncoder(args).to(args.device)

# --- Load pretrained VAE encoder if provided ---
if args.pretrained_vae_path is not None and args.pretrained_vae_path.lower() != 'none':
    try:
        vae_ckpt = torch.load(args.pretrained_vae_path, map_location=args.device)
        enc_state = {k.replace('encoder.', ''): v for k, v in vae_ckpt['state_dict'].items() if k.startswith('encoder.')}
        model.encoder.load_state_dict(enc_state, strict=False)
        logger.info(f"Loaded encoder weights from pretrained VAE {args.pretrained_vae_path}")
    except Exception as e:
        logger.error(f"Failed to load pretrained VAE encoder: {e}")

if args.resume is not None:
    try:
        logger.info(f'Resuming from checkpoint: {args.resume}')
        ckpt = torch.load(args.resume, map_location=args.device)
        model.load_state_dict(ckpt['state_dict'])
        logger.info('Checkpoint loaded successfully.')
        # Optionally load optimizer and scheduler states if saved in ckpt['others']
        # if 'others' in ckpt and 'optimizer' in ckpt['others']:
        #     optimizer.load_state_dict(ckpt['others']['optimizer'])
        # if 'others' in ckpt and 'scheduler' in ckpt['others']:
        #     scheduler.load_state_dict(ckpt['others']['scheduler'])
    except FileNotFoundError:
        logger.error(f"Checkpoint file not found: {args.resume}. Starting from scratch.")
    except Exception as e:
        logger.error(f"Error loading checkpoint: {e}. Starting from scratch.")

logger.info(repr(model))

# EMA (指数移动平均)
ema_wrapper = None
if args.use_ema:
    ema_wrapper = EMAWrapper(model, decay=args.ema_decay, device=args.device)
    
    # 如果从checkpoint恢复，尝试加载EMA状态
    if args.resume is not None:
        try:
            ckpt = torch.load(args.resume, map_location=args.device)
            if 'others' in ckpt and 'ema' in ckpt['others']:
                ema_wrapper.load_state_dict(ckpt['others']['ema'])
                logger.info('EMA state loaded from checkpoint.')
            else:
                logger.info('No EMA state found in checkpoint, using fresh EMA initialization.')
        except Exception as e:
            logger.warning(f"Failed to load EMA state: {e}. Using fresh EMA initialization.")
    
    logger.info(f'EMA initialized with decay={args.ema_decay}')
else:
    logger.info('EMA disabled')

# Optionally freeze encoder
if args.freeze_encoder_after:
    for p in model.encoder.parameters():
        p.requires_grad = False
    logger.info('Encoder frozen as per flag.')
    model.encoder.eval()  # BatchNorm layers use eval statistics

# Freeze encoder params considered in optim below
trainable_params = [p for p in model.parameters() if p.requires_grad]
optimizer = torch.optim.AdamW(trainable_params, lr=args.lr, weight_decay=args.weight_decay)

logger.info(f"Setting up learning rate schedule: {args.warmup_iters} warmup iters, then cosine decay.")

# 1. 线性预热调度器
warmup_sched = LinearLR(
    optimizer,
    start_factor=1e-5,      # 从 lr * start_factor 开始
    end_factor=1.0,         # 预热到 lr * end_factor
    total_iters=args.warmup_iters
)

# 2. 余弦衰减调度器
cosine_sched = CosineAnnealingLR(
    optimizer,
    T_max=args.max_iters - args.warmup_iters, # 衰减的总步数
    eta_min=1e-6                             # 最小学习率
)

# 3. 将两者串联起来
scheduler = SequentialLR(
    optimizer,
    schedulers=[warmup_sched, cosine_sched],
    milestones=[args.warmup_iters] # 在第 warmup_iters 步时，从 warmup_sched 切换到 cosine_sched
)
# Train, validate
def train(it):
    """
    执行单次训练迭代。
    该函数从预计算数据集中加载数据进行训练。
    """
    global train_iter  # 声明 train_iter 是全局变量
    
    # 确保模型处于训练模式
    model.train()
    
    # 从无限循环的数据迭代器中获取下一批数据
    try:
        batch = next(train_iter)
        if batch is None:
            logger.warning(f"跳过迭代 {it}，数据加载器返回空批次。")
            return
    except StopIteration:
        # 如果数据加载器完成一个epoch，重新初始化它
        logger.info("重新初始化训练数据迭代器。")
        train_iter = get_data_iterator(train_loader)
        batch = next(train_iter)

    # 1. 从数据加载器中解包数据
    pcs_anom = batch['pointcloud_anom'].to(args.device)
    pcs_norm = batch['pointcloud_norm'].to(args.device)
    defect_labels = batch['defect_label'].to(args.device)

    # 2. 准备用于Diffusion模型的上下文(context)
    #    在no_grad()下运行冻结的Encoder，因为它只作为特征提取器
    with torch.no_grad():
        # Encoder只看“正常”点云
        # 假设您的VAE Encoder返回 (mu, logvar)
        latent_z, _ = model.encoder(pcs_norm)
    
    # 获取缺陷标签对应的嵌入向量
    label_vec = model.label_emb(defect_labels)
    
    # 将潜在编码和标签嵌入拼接起来，形成最终的上下文向量
    context = torch.cat([latent_z, label_vec], dim=1)
    latent_z_norm = torch.linalg.norm(latent_z, dim=1).mean().item()
    label_vec_norm = torch.linalg.norm(label_vec, dim=1).mean().item()
    if it % 200 == 0: # 每200步打印一次
        logger.info(f"Norms - latent_z: {latent_z_norm:.4f}, label_vec: {label_vec_norm:.4f}")
    # 3. 计算损失函数
    # 扩散模型(Decoder)的目标是：在给定上下文的条件下，重建出“异常点云”。
    loss, diff_components = model.diffusion_decoder.get_loss(x_0=pcs_anom, context=context)
    
    # 4. 执行反向传播和模型优化
    optimizer.zero_grad()
    loss.backward()
    
    if args.max_grad_norm is not None and args.max_grad_norm > 0:
        trainable_params = [p for p in model.parameters() if p.requires_grad]
        grad_norm = clip_grad_norm_(trainable_params, args.max_grad_norm)
    else:
        grad_norm = torch.tensor(0.0)
        
    optimizer.step()
    scheduler.step()
    
    if ema_wrapper is not None:
        ema_wrapper.update()

    # 5. 记录日志
    if it % 50 == 0:
        logger.info(
            f'[Train] Iter {it:06d} | Loss {loss.item():.6f} | '
            f'Grad Norm {grad_norm.item():.4f} | LR {optimizer.param_groups[0]["lr"]:.6e}'
        )
        if args.logging:
            writer.add_scalar('train/loss_total', loss.item(), it)
            writer.add_scalar('train/lr', optimizer.param_groups[0]['lr'], it)
            writer.add_scalar('train/grad_norm', grad_norm.item(), it)
            for key, value in diff_components.items():
                val_to_log = value.item() if isinstance(value, torch.Tensor) else value
                if isinstance(val_to_log, (int, float)):
                    writer.add_scalar(f'train/loss_{key}', val_to_log, it)
            writer.flush()

def validate_loss(it):
    # 初始化用于存储各批次平均指标的列表
    val_loss_list = []
    val_cd_list = []
    val_emd_list = []
    
    # 用于可视化的数据 (只保存第一个批次)
    first_batch_refs = None
    first_batch_recons = None

    model.eval()
    if len(val_loader) == 0:
        logger.warning("Validation loader is empty, skipping validation.")
        return float('inf')

    # 使用EMA权重进行验证
    validation_context = ema_wrapper.average_parameters() if ema_wrapper is not None else None
    
    with torch.no_grad():
        if validation_context is not None:
            validation_context.__enter__()
        try:
            pbar = tqdm(val_loader, desc=f'Validate_Iter_{it}', leave=False)
            for i, batch in enumerate(pbar):
                if i >= args.num_val_batches: # 限制验证的批次数
                    break
                if batch is None: continue

                ref_norm = batch['pointcloud'].to(args.device)
                Bv, Nv, _ = ref_norm.shape

                # 1. 准备上下文 (Context)
                latent_val = model.encode(ref_norm, return_log_variance=False)
                normal_idx = torch.zeros(Bv, dtype=torch.long, device=args.device)
                label_vec_val = model.label_emb(normal_idx)
                context_val = torch.cat([latent_val, label_vec_val], dim=1)

                # 2. 计算验证损失 (Diffusion模型的重建损失)
                val_loss_i, _ = model.diffusion_decoder.get_loss(x_0=ref_norm, context=context_val)
                val_loss_list.append(val_loss_i.item())

                # 3. 生成重建点云用于CD/EMD计算
                recons_norm = model.decode(context_val, ref_norm.size(1), sample_steps=args.sample_steps_decode)
                recons_norm = recons_norm.to(args.device)
                
                # 4. 逐批次计算CD和EMD
                # CD
                dl, dr = distChamfer(recons_norm, ref_norm)
                
                # !!! 核心修正 !!!
                # 假设 dl 和 dr 是每个点云的距离平方总和 (shape: [Bv])
                # 我们需要先除以点数 Nv，得到每个点云的平均CD，然后再对批次求平均
                batch_cd_avg = ((dl / Nv) + (dr / Nv)).mean().item()
                val_cd_list.append(batch_cd_avg)
                
                # EMD (这个计算是正确的)
                try:
                    emd_val = earth_mover_distance(recons_norm.contiguous(), ref_norm.contiguous(), transpose=False)
                    batch_emd_avg = emd_val.mean().item()
                    val_emd_list.append(batch_emd_avg)
                except Exception as e:
                    logger.warning(f"Could not compute EMD for a batch: {e}")

                # 更新进度条
                pbar.set_postfix({
                    'Loss': f'{np.mean(val_loss_list):.4f}',
                    'CD': f'{np.mean(val_cd_list):.6f}', 
                    'EMD': f'{np.mean(val_emd_list):.6f}' if val_emd_list else 'N/A'
                })

                # 保存第一个批次的数据用于可视化
                if i == 0:
                    # 从batch中获取shift和scale (它们当前在CPU上)
                    shift = batch.get('shift', torch.zeros(Bv, 1, 3))
                    scale = batch.get('scale', torch.ones(Bv, 1, 1))

                    # !!! 核心修正: 将 shift 和 scale 移动到 GPU 上 !!!
                    shift = shift.to(args.device)
                    scale = scale.to(args.device)

                    # 现在 ref_norm, recons_norm, scale, shift 都在 GPU 上，可以安全地计算
                    first_batch_refs = (ref_norm * scale + shift).cpu()
                    first_batch_recons = (recons_norm * scale + shift).cpu()

        finally:
            if validation_context is not None:
                validation_context.__exit__(None, None, None)

    # 5. 计算所有批次的平均指标
    avg_val_loss = np.mean(val_loss_list) if val_loss_list else 0.0
    avg_cd = np.mean(val_cd_list) if val_cd_list else float('inf')
    avg_emd = np.mean(val_emd_list) if val_emd_list else float('inf')

    logger.info(f'[Val] Iter {it:06d} | Avg Loss {avg_val_loss:.6f} | CD(norm) {avg_cd:.6f} | EMD(norm) {avg_emd:.6f}')

    # 6. 记录到TensorBoard并进行可视化 (这部分代码无需修改)
    if args.logging:
        writer.add_scalar('val/loss_avg', avg_val_loss, it)
        writer.add_scalar('val/metric_cd', avg_cd, it)
        writer.add_scalar('val/metric_emd', avg_emd, it)

        num_vis = min(args.num_inspect_pointclouds, args.val_batch_size)
        if first_batch_refs is not None and first_batch_recons is not None:
            writer.add_mesh('val/originals', first_batch_refs[:num_vis], global_step=it)
            writer.add_mesh('val/reconstructions', first_batch_recons[:num_vis], global_step=it)

            pcd_save_dir = os.path.join(log_dir, "pcd_val_outputs", f"iter_{it}")
            os.makedirs(pcd_save_dir, exist_ok=True)
            for i in range(min(num_vis, first_batch_refs.shape[0])):
                pcd_ref = o3d.geometry.PointCloud()
                pcd_ref.points = o3d.utility.Vector3dVector(first_batch_refs[i].numpy())
                o3d.io.write_point_cloud(os.path.join(pcd_save_dir, f"ref_{i}.pcd"), pcd_ref)
                
                pcd_recon = o3d.geometry.PointCloud()
                pcd_recon.points = o3d.utility.Vector3dVector(first_batch_recons[i].numpy())
                o3d.io.write_point_cloud(os.path.join(pcd_save_dir, f"recon_{i}.pcd"), pcd_recon)
        writer.flush()

    return avg_cd

# Main loop
logger.info('Start training...')
try:
    it = getattr(args, 'start_iter', 1) # Allow resuming iteration count if loaded from ckpt args
    min_val_metric_for_ckpt = float('inf')

    while it <= args.max_iters:
        train(it)

        if it % args.val_freq == 0 or it == args.max_iters:
            current_val_metric = validate_loss(it)

            if args.logging: # Ensure ckpt_mgr is only used if logging is enabled
                # Save checkpoint based on validation metric (e.g., lower CD is better)
                is_best = current_val_metric < min_val_metric_for_ckpt
                if is_best:
                    min_val_metric_for_ckpt = current_val_metric
                    logger.info(f"New best validation metric (CD): {min_val_metric_for_ckpt:.6f} at iteration {it}. Saving best model.")

                # CheckpointManager saves regular checkpoints and handles 'best' model link
                opt_states = {
                    'optimizer': optimizer.state_dict(),
                    'scheduler': scheduler.state_dict(),
                    'iteration': it, # Save current iteration
                    'best_val_metric': min_val_metric_for_ckpt
                }
                # 保存EMA状态
                if ema_wrapper is not None:
                    opt_states['ema'] = ema_wrapper.state_dict()
                ckpt_mgr.save(model, args, current_val_metric, others=opt_states, step=it)


        it += 1

except KeyboardInterrupt:
    logger.info('Terminating training...')
except Exception as e:
    logger.error(f"An error occurred during training: {e}", exc_info=True)
finally:
    if args.logging and writer is not None: # Ensure writer is not BlackHole
        writer.close()
    logger.info("Training finished or terminated.")
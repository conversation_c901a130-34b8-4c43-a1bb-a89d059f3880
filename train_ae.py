# DDP MODIFICATION: 导入DDP相关的模块
import torch.distributed as dist
from torch.nn.parallel import DistributedDataParallel as DDP
from torch.utils.data.distributed import DistributedSampler
# DDP MODIFICATION: END

import os
import argparse
import torch
import torch.utils.tensorboard
from torch.nn.utils import clip_grad_norm_
from tqdm.auto import tqdm
import numpy as np
import open3d as o3d # Added for PCD saving
from evaluation.evaluation_metrics import distChamfer
from emd import earth_mover_distance
from utils.dataset import *
from utils.misc import *
from utils.data import *
from utils.transform import *
from utils.ema import EMAWrapper
from models.autoencoder import *
from evaluation import EMD_CD
from torch.optim.lr_scheduler import LinearLR, CosineAnnealingLR, SequentialLR

THOUSAND = 1000

# ##############################################################################
# DDP MODIFICATION:
# HOW TO RUN THIS SCRIPT:
# torchrun --nproc_per_node=[NUMBER_OF_GPUS] train_ae.py [YOUR_ARGUMENTS]
# Example for 2 GPUs:
# torchrun --nproc_per_node=2 train_ae.py --categories airplane --train_batch_size 2
# ##############################################################################


# Arguments
parser = argparse.ArgumentParser()

# --- 模型参数 ---
parser.add_argument('--latent_dim', type=int, default=256, help='Dimension of the latent space.')
parser.add_argument('--model_type', type=str, default='vae', choices=['ae', 'vae'], help='ae or vae architecture')
parser.add_argument('--resume', type=str, default=None, help='Path to checkpoint to resume training from.')

# --- VAE 相关参数 ---
parser.add_argument('--use_vae', type=eval, default=True, choices=[True, False], help='deprecated, ignored')
parser.add_argument('--kl_weight', type=float, default=0.001, help='Weight for the KL divergence term in VAE loss')

# --- 扩散解码器 (内部DiffusionPoint) 相关参数 ---
parser.add_argument('--num_steps', type=int, default=1000, help='Number of diffusion steps (T) for the decoder.')
parser.add_argument('--beta_1', type=float, default=1e-4, help='Initial beta value for variance schedule of the decoder.')
parser.add_argument('--beta_T', type=float, default=0.02, help='Final beta value for variance schedule of the decoder.')
parser.add_argument('--sched_mode', type=str, default='cosine', help='Type of variance schedule for the diffusion decoder (linear or cosine).')
parser.add_argument('--learn_sigma_diffusion', type=eval, default=True, choices=[True, False], help='Whether the diffusion decoder learns its variance.')
parser.add_argument('--residual_within_block_diffusion', type=eval, default=True, choices=[True, False], help='Whether PointwiseNet attention blocks in decoder use internal residual connections.')
parser.add_argument('--time_emb_dim_diffusion', type=int, default=128, help='Dimension of the sinusoidal time embedding in the diffusion decoder PointwiseNet.')
parser.add_argument('--lambda_vlb_diffusion', type=float, default=1e-3, help='Weight of the KL/VLB term in diffusion loss.')
# --- CFG 相关参数 ---
parser.add_argument('--cfg_drop_prob', type=float, default=0.1, help='训练时条件随机丢弃的概率 (0表示不丢弃)')
parser.add_argument('--cfg_scale', type=float, default=4.0, help='采样时CFG的引导强度 (1.0表示无引导)')
# --- 数据集和加载器参数 ---
parser.add_argument('--dataset_path', type=str, default='/home/<USER>/llm/3DAD/data/Real3D-AD-PCD', help='Path to the dataset.')
parser.add_argument('--categories', type=str_list, default=['airplane'], help='List of categories to use.')
parser.add_argument('--scale_mode', type=str, default='shape_bbox', help='Point cloud normalization mode.')
parser.add_argument('--train_batch_size', type=int, default=4, help='Batch size for training *per GPU*.')
parser.add_argument('--val_batch_size', type=int, default=4, help='Batch size for validation.')
parser.add_argument('--num_points', type=int, default=2048, help='Number of points per point cloud.')
parser.add_argument('--sampling_method', type=str, default='hybrid', help='Point cloud sampling method.')
parser.add_argument('--rotate', type=eval, default=False, choices=[True, False], help='Whether to apply random rotation augmentation.')
parser.add_argument('--precomputed_defect_path', type=str, default="./defect_generations_strong", help='预计算缺陷样本的路径 (例如 ./defect_generations_strong)。')
parser.add_argument('--processed_data_path', type=str, default='./processed_data')
# --- 优化器和调度器参数 ---
parser.add_argument('--lr', type=float, default=1e-4, help='Learning rate.') # VAEs/Diffusion might benefit from smaller LRs
parser.add_argument('--weight_decay', type=float, default=0.0, help='Weight decay for the optimizer.')
parser.add_argument('--max_grad_norm', type=float, default=1.0, help='Maximum gradient norm for clipping.')

# --- EMA 相关参数 ---
parser.add_argument('--use_ema', type=eval, default=True, choices=[True, False], help='Whether to use EMA for model weights.')
parser.add_argument('--ema_decay', type=float, default=0.9999, help='EMA decay rate. Typical values: 0.9999 for stable training, 0.999 for faster adaptation.')

# --- 训练参数 ---
parser.add_argument('--seed', type=int, default=2024, help='Random seed for reproducibility.')
parser.add_argument('--logging', type=eval, default=True, choices=[True, False], help='Enable or disable logging.')
parser.add_argument('--log_root', type=str, default='./logs_ae_vae', help='Root directory for logs.')
# DDP MODIFICATION: device arugment is no longer used, rank will be used instead.
# parser.add_argument('--device', type=str, default='cuda', help='Device to use for training (e.g., "cuda", "cpu").')
parser.add_argument('--max_iters', type=int, default=50000, help='Maximum number of training iterations.') # Increased iterations
parser.add_argument('--val_freq', type=int, default=2000, help='Frequency of validation (in iterations).')
parser.add_argument('--tag', type=str, default=None, help='Optional tag for the experiment log directory.')
parser.add_argument('--num_workers', type=int, default=4, help='Number of workers for DataLoader.')
parser.add_argument('--warmup_iters', type=int, default=2500, help='Number of linear warmup iterations.')

# --- 验证/检测/采样 参数 ---
parser.add_argument('--num_val_batches', type=int, default=4, help='Number of batches to use for validation loss calculation.') # Increased for more stable val
parser.add_argument('--num_inspect_batches', type=int, default=1, help='Number of batches for detailed inspection/visualization.')
parser.add_argument('--num_inspect_pointclouds', type=int, default=2, help='Number of point clouds to visualize during inspection.')
parser.add_argument('--ret_traj', type=eval, default=False, choices=[True, False], help='Whether to return the full trajectory during decoding/sampling.')
parser.add_argument('--sample_steps_decode', type=int, default=None, help='Number of steps for faster decoding sampler (if None, use full diffusion steps). Used in model.decode.')
parser.add_argument('--flexibility', type=float, default=0.0, help='Flexibility for sampling (passed to diffusion.sample if used, currently not directly used by improved_ddpm sample interface)')
parser.add_argument('--metrics_in_normalized_space', type=eval, default=True, choices=[True, False],
                    help='Whether to compute CD/EMD metrics in normalized space (True) or original space (False). Normalized space avoids scale inconsistency.')

# --- 预训练 VAE ---
parser.add_argument('--pretrained_vae_path', type=str, default="/home/<USER>/llm/3DAD/diffusion-point-cloud/logs_vae/VAE_2025_07_28__13_32_48/model_best.pt", help='Path to pretrained VAE checkpoint (encoder weights will be loaded)')

# Freeze encoder after loading pretrained VAE
parser.add_argument('--freeze_encoder_after', type=eval, default=True, choices=[True, False], help='Freeze encoder parameters after loading pretrained weights.')

# --- 缺陷类型及标签嵌入 ---
parser.add_argument('--num_defect_types', type=int, default=3, help='0: normal, 1: bulge, 2: sink')
parser.add_argument('--label_emb_dim', type=int, default=8, help='Embedding dimension for defect type label.')

args = parser.parse_args()
seed_all(args.seed)

# DDP MODIFICATION: Initialize the process group and get rank
def setup_ddp():
    """Initializes DDP process group."""
    dist.init_process_group(backend="nccl")
    torch.cuda.set_device(int(os.environ["LOCAL_RANK"]))

setup_ddp()
rank = int(os.environ["LOCAL_RANK"])
world_size = int(os.environ["WORLD_SIZE"])
# DDP MODIFICATION: END

# determine if using VAE based on model_type or legacy flag
use_vae_flag = (args.model_type.lower() == 'vae')

# DDP MODIFICATION: Only the main process (rank 0) should handle logging and file I/O.
log_dir = None
logger = None
writer = None
ckpt_mgr = None
if rank == 0:
    if args.logging:
        log_dir = get_new_log_dir(args.log_root, prefix='AE_VAE_', postfix='_' + args.tag if args.tag is not None else '')
        logger = get_logger('train', log_dir)
        writer = torch.utils.tensorboard.SummaryWriter(log_dir)
        ckpt_mgr = CheckpointManager(log_dir, max_to_keep=5)
        log_hyperparams(writer, args)
    else:
        logger = get_logger('train', None)
        writer = BlackHole()
        ckpt_mgr = BlackHole()
    logger.info(args)
# For non-main processes, use dummy objects to avoid errors
else:
    logger = BlackHole()
    writer = BlackHole()
    ckpt_mgr = BlackHole()
# DDP MODIFICATION: END


# Datasets and loaders
if rank == 0:
    logger.info('Loading datasets...')

# This part of the code is executed by all processes, but the dataset is loaded from disk,
# so it's fine. The DistributedSampler will ensure each process gets its own slice of data.
raw_data_provider = Real3DADDataset(
    path=args.dataset_path,
    categories=args.categories,
    split='train',
    scale_mode=None,
    transform=None,
    num_points=None,
    sampling_method='none'
)

train_dset = FastPreprocessedDataset(root_dir=args.processed_data_path)

val_dset = ValidationDataset(
    raw_data_source=raw_data_provider,
    scale_mode=args.scale_mode,
    num_points=args.num_points,
    sampling_method=args.sampling_method
)

if rank == 0:
    logger.info(f"训练集 (使用 PrecomputedDefectDataset 统一处理): {len(train_dset)} 个样本")
    logger.info(f"验证集 (使用 ValidationDataset 统一处理): {len(val_dset)} 个样本")

# DDP MODIFICATION: Use DistributedSampler for the training set
train_sampler = DistributedSampler(train_dset, num_replicas=world_size, rank=rank, shuffle=True)
val_sampler = DistributedSampler(val_dset, num_replicas=world_size, rank=rank, shuffle=False) # Optional for validation

train_loader = DataLoader(
    train_dset,
    batch_size=args.train_batch_size,
    shuffle=False, # Shuffle is handled by the sampler
    num_workers=args.num_workers,
    pin_memory=True,
    collate_fn=custom_collate_fn,
    sampler=train_sampler # Provide the sampler
)
train_iter = get_data_iterator(train_loader)

val_loader = DataLoader(
    val_dset,
    batch_size=args.val_batch_size,
    shuffle=False,
    num_workers=args.num_workers,
    collate_fn=custom_collate_fn,
    sampler=val_sampler # Provide the sampler for validation
)
# DDP MODIFICATION: END

# Model
if rank == 0:
    logger.info('Building model...')

# DDP MODIFICATION: Move model to the correct device for the current process (rank)
device = rank
model = AutoEncoder(args).to(device)
if torch.__version__.startswith("2"):
    model = torch.compile(model)

# DDP MODIFICATION: Load checkpoints with correct map_location
map_location = {'cuda:%d' % 0: 'cuda:%d' % rank}

# --- Load pretrained VAE encoder if provided ---
if args.pretrained_vae_path is not None and args.pretrained_vae_path.lower() != 'none':
    try:
        vae_ckpt = torch.load(args.pretrained_vae_path, map_location=map_location, weights_only=False)
        enc_state = {k.replace('encoder.', ''): v for k, v in vae_ckpt['state_dict'].items() if k.startswith('encoder.')}
        model.encoder.load_state_dict(enc_state, strict=False)
        if rank == 0:
            logger.info(f"Loaded encoder weights from pretrained VAE {args.pretrained_vae_path}")
    except Exception as e:
        if rank == 0:
            logger.error(f"Failed to load pretrained VAE encoder: {e}")

if args.resume is not None:
    try:
        if rank == 0:
            logger.info(f'Resuming from checkpoint: {args.resume}')
        ckpt = torch.load(args.resume, map_location=map_location)
        model.load_state_dict(ckpt['state_dict'])
        if rank == 0:
            logger.info('Checkpoint loaded successfully.')
    except FileNotFoundError:
        if rank == 0:
            logger.error(f"Checkpoint file not found: {args.resume}. Starting from scratch.")
    except Exception as e:
        if rank == 0:
            logger.error(f"Error loading checkpoint: {e}. Starting from scratch.")

# DDP MODIFICATION: Wrap the model with DDP *after* loading weights and moving to device
model = DDP(model, device_ids=[rank])
# DDP MODIFICATION: END

if rank == 0:
    # Use model.module to access original model attributes
    logger.info(repr(model.module))

# EMA (指数移动平均)
ema_wrapper = None
if args.use_ema:
    # EMA should wrap the original model, not the DDP wrapper
    ema_wrapper = EMAWrapper(model.module, decay=args.ema_decay)
    if rank == 0:
        logger.info(f'EMA initialized with decay={args.ema_decay}')
else:
    if rank == 0:
        logger.info('EMA disabled')

# Optionally freeze encoder
if args.freeze_encoder_after:
    # Access encoder through model.module
    for p in model.module.encoder.parameters():
        p.requires_grad = False
    if rank == 0:
        logger.info('Encoder frozen as per flag.')
    model.module.encoder.eval()

# Freeze encoder params considered in optim below
trainable_params = [p for p in model.parameters() if p.requires_grad]
optimizer = torch.optim.AdamW(trainable_params, lr=args.lr, weight_decay=args.weight_decay)

if rank == 0:
    logger.info(f"Setting up learning rate schedule: {args.warmup_iters} warmup iters, then cosine decay.")

# Schedulers are the same
warmup_sched = LinearLR(optimizer, start_factor=1e-5, end_factor=1.0, total_iters=args.warmup_iters)
cosine_sched = CosineAnnealingLR(optimizer, T_max=args.max_iters - args.warmup_iters, eta_min=1e-6)
scheduler = SequentialLR(optimizer, schedulers=[warmup_sched, cosine_sched], milestones=[args.warmup_iters])


# Train, validate
def train(it):
    global train_iter
    
    # DDP MODIFICATION: Set sampler epoch to ensure proper shuffling
    train_sampler.set_epoch(it)

    model.train()
    
    try:
        batch = next(train_iter)
        if batch is None:
            if rank == 0:
                logger.warning(f"跳过迭代 {it}，数据加载器返回空批次。")
            return
    except StopIteration:
        if rank == 0:
            logger.info("重新初始化训练数据迭代器。")
        train_iter = get_data_iterator(train_loader)
        batch = next(train_iter)

    # DDP MODIFICATION: Move data to the correct device (rank)
    device = rank
    pcs_anom = batch['pointcloud_anom'].to(device)
    pcs_norm = batch['pointcloud_norm'].to(device)
    defect_labels = batch['defect_label'].to(device)

    with torch.no_grad():
        # DDP MODIFICATION: Access model attributes via model.module
        latent_z, _ = model.module.encoder(pcs_norm)

    # --- CFG MODIFICATION: 训练时随机丢弃条件 ---
    if args.cfg_drop_prob > 0:
        # 创建一个随机掩码，True代表要丢弃
        drop_mask = torch.rand(defect_labels.size(0), device=device) < args.cfg_drop_prob
        # 获取无条件标签的ID
        uncond_label_id = model.module.uncond_label_id
        # 将掩码为True的位置的标签替换为无条件标签
        defect_labels[drop_mask] = uncond_label_id
    # --- CFG MODIFICATION: END ---

    label_vec = model.module.label_emb(defect_labels)
    context = torch.cat([latent_z, label_vec], dim=1)
    
    # DDP MODIFICATION: Only log from the main process
    if it % 200 == 0 and rank == 0:
        latent_z_norm = torch.linalg.norm(latent_z, dim=1).mean().item()
        label_vec_norm = torch.linalg.norm(label_vec, dim=1).mean().item()
        logger.info(f"Norms - latent_z: {latent_z_norm:.4f}, label_vec: {label_vec_norm:.4f}")
    
    loss, diff_components = model.module.diffusion_decoder.get_loss(x_0=pcs_anom, context=context)
    
    optimizer.zero_grad()
    loss.backward()
    
    if args.max_grad_norm is not None and args.max_grad_norm > 0:
        trainable_params = [p for p in model.parameters() if p.requires_grad]
        grad_norm = clip_grad_norm_(trainable_params, args.max_grad_norm)
    else:
        grad_norm = torch.tensor(0.0)
        
    optimizer.step()
    scheduler.step()
    
    if ema_wrapper is not None:
        ema_wrapper.update()

    # DDP MODIFICATION: Only log from the main process
    if it % 50 == 0 and rank == 0:
        logger.info(
            f'[Train] Iter {it:06d} | Loss {loss.item():.6f} | '
            f'Grad Norm {grad_norm.item():.4f} | LR {optimizer.param_groups[0]["lr"]:.6e}'
        )
        if args.logging:
            writer.add_scalar('train/loss_total', loss.item(), it)
            writer.add_scalar('train/lr', optimizer.param_groups[0]['lr'], it)
            writer.add_scalar('train/grad_norm', grad_norm.item(), it)
            for key, value in diff_components.items():
                val_to_log = value.item() if isinstance(value, torch.Tensor) else value
                if isinstance(val_to_log, (int, float)):
                    writer.add_scalar(f'train/loss_{key}', val_to_log, it)
            writer.flush()

def validate_loss(it):
    val_loss_list = []
    val_cd_list = []
    val_emd_list = []
    
    first_batch_refs = None
    first_batch_recons = None

    # DDP MODIFICATION: Use model.module for evaluation
    model.eval()
    
    if len(val_loader) == 0:
        if rank == 0:
            logger.warning("Validation loader is empty, skipping validation.")
        return float('inf')

    validation_context = ema_wrapper.average_parameters() if ema_wrapper is not None else None
    
    with torch.no_grad():
        if validation_context is not None:
            validation_context.__enter__()
        try:
            # DDP MODIFICATION: Only show progress bar on the main process
            pbar = tqdm(val_loader, desc=f'Validate_Iter_{it}', leave=False, disable=(rank != 0))
            for i, batch in enumerate(pbar):
                if i >= args.num_val_batches:
                    break
                if batch is None: continue

                # DDP MODIFICATION: Move data to correct device
                device = rank
                ref_norm = batch['pointcloud'].to(device)
                Bv, Nv, _ = ref_norm.shape

                # DDP MODIFICATION: Access model attributes via .module
                latent_val = model.module.encode(ref_norm, return_log_variance=False)
                normal_idx = torch.zeros(Bv, dtype=torch.long, device=device)
                label_vec_val = model.module.label_emb(normal_idx)
                context_val = torch.cat([latent_val, label_vec_val], dim=1)

                val_loss_i, _ = model.module.diffusion_decoder.get_loss(x_0=ref_norm, context=context_val)
                
                # DDP MODIFICATION: Gather results from all GPUs for accurate metrics
                val_loss_tensor = torch.tensor(val_loss_i.item(), device=device)
                dist.all_reduce(val_loss_tensor, op=dist.ReduceOp.SUM)
                val_loss_list.append(val_loss_tensor.item() / world_size)

                recons_norm = model.module.decode(context_val, ref_norm.size(1), sample_steps=args.sample_steps_decode, guidance_scale=args.cfg_scale)
                recons_norm = recons_norm.to(device)

                if rank == 0 and i == 0: # 只打印第一个验证批次的信息
                    logger.info("--- 验证点云坐标范围检查 ---")
                    logger.info(f"基准点云 ref_norm min: {ref_norm.min().item():.4f}, max: {ref_norm.max().item():.4f}")
                    logger.info(f"生成点云 recons_norm min: {recons_norm.min().item():.4f}, max: {recons_norm.max().item():.4f}")
                    logger.info("---------------------------------")
                    
                dl, dr = distChamfer(recons_norm, ref_norm)
                batch_cd_avg = ((dl / Nv) + (dr / Nv)).mean()
                dist.all_reduce(batch_cd_avg, op=dist.ReduceOp.SUM)
                val_cd_list.append(batch_cd_avg.item() / world_size)
                
                try:
                    emd_val = earth_mover_distance(recons_norm.contiguous(), ref_norm.contiguous(), transpose=False)
                    batch_emd_avg = (emd_val / Nv).mean()
                    dist.all_reduce(batch_emd_avg, op=dist.ReduceOp.SUM)
                    val_emd_list.append(batch_emd_avg.item() / world_size)
                except Exception as e:
                    if rank == 0:
                        logger.warning(f"Could not compute EMD for a batch: {e}")

                # DDP MODIFICATION: Only main process updates progress bar
                if rank == 0:
                    pbar.set_postfix({
                        'Loss': f'{np.mean(val_loss_list):.4f}',
                        'CD': f'{np.mean(val_cd_list):.6f}',
                        'EMD': f'{np.mean(val_emd_list):.6f}' if val_emd_list else 'N/A'
                    })

                # DDP MODIFICATION: Only main process saves visualizations
                if i == 0 and rank == 0:
                    shift = batch.get('shift', torch.zeros(Bv, 1, 3)).to(device)
                    scale = batch.get('scale', torch.ones(Bv, 1, 1)).to(device)
                    first_batch_refs = (ref_norm * scale + shift).cpu()
                    first_batch_recons = (recons_norm * scale + shift).cpu()

        finally:
            if validation_context is not None:
                validation_context.__exit__(None, None, None)

    avg_val_loss = np.mean(val_loss_list) if val_loss_list else 0.0
    avg_cd = np.mean(val_cd_list) if val_cd_list else float('inf')
    avg_emd = np.mean(val_emd_list) if val_emd_list else float('inf')

    # DDP MODIFICATION: Only log and write from main process
    if rank == 0:
        logger.info(f'[Val] Iter {it:06d} | Avg Loss {avg_val_loss:.6f} | CD(norm) {avg_cd:.6f} | EMD(norm) {avg_emd:.6f}')
        if args.logging:
            # ... (the rest of the logging and visualization code is the same, but wrapped in if rank == 0) ...
            writer.add_scalar('val/loss_avg', avg_val_loss, it)
            writer.add_scalar('val/metric_cd', avg_cd, it)
            writer.add_scalar('val/metric_emd', avg_emd, it)

            num_vis = min(args.num_inspect_pointclouds, args.val_batch_size)
            if first_batch_refs is not None and first_batch_recons is not None:
                writer.add_mesh('val/originals', first_batch_refs[:num_vis], global_step=it)
                writer.add_mesh('val/reconstructions', first_batch_recons[:num_vis], global_step=it)

                pcd_save_dir = os.path.join(log_dir, "pcd_val_outputs", f"iter_{it}")
                os.makedirs(pcd_save_dir, exist_ok=True)
                for i in range(min(num_vis, first_batch_refs.shape[0])):
                    pcd_ref = o3d.geometry.PointCloud()
                    pcd_ref.points = o3d.utility.Vector3dVector(first_batch_refs[i].numpy())
                    o3d.io.write_point_cloud(os.path.join(pcd_save_dir, f"ref_{i}.pcd"), pcd_ref)
                    
                    pcd_recon = o3d.geometry.PointCloud()
                    pcd_recon.points = o3d.utility.Vector3dVector(first_batch_recons[i].numpy())
                    o3d.io.write_point_cloud(os.path.join(pcd_save_dir, f"recon_{i}.pcd"), pcd_recon)
            writer.flush()

    return avg_cd

# Main loop
if rank == 0:
    logger.info('Start training...')
try:
    it = getattr(args, 'start_iter', 1)
    min_val_metric_for_ckpt = float('inf')

    while it <= args.max_iters:
        train(it)

        if it % args.val_freq == 0 or it == args.max_iters:
            current_val_metric = validate_loss(it)

            # DDP MODIFICATION: Only save checkpoints from the main process
            if rank == 0:
                if args.logging:
                    is_best = current_val_metric < min_val_metric_for_ckpt
                    if is_best:
                        min_val_metric_for_ckpt = current_val_metric
                        logger.info(f"New best validation metric (CD): {min_val_metric_for_ckpt:.6f} at iteration {it}. Saving best model.")
                    
                    opt_states = {
                        'optimizer': optimizer.state_dict(),
                        'scheduler': scheduler.state_dict(),
                        'iteration': it,
                        'best_val_metric': min_val_metric_for_ckpt
                    }
                    if ema_wrapper is not None:
                        opt_states['ema'] = ema_wrapper.state_dict()
                    
                    # DDP MODIFICATION: Save model.module.state_dict()
                    ckpt_mgr.save(model.module, args, current_val_metric, others=opt_states, step=it)

        it += 1

except KeyboardInterrupt:
    if rank == 0:
        logger.info('Terminating training...')
except Exception as e:
    if rank == 0:
        logger.error(f"An error occurred during training: {e}", exc_info=True)
finally:
    # DDP MODIFICATION: Final cleanup
    if rank == 0:
        if args.logging and writer is not None:
            writer.close()
        logger.info("Training finished or terminated.")
    dist.destroy_process_group()
#!/usr/bin/env python
"""
从指定目录加载多个PCD文件，并使用Matplotlib在单个图像中进行可视化。
"""
import os
import argparse
import open3d as o3d
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

def setup_3d_axis(ax, x_lim, y_lim, z_lim, elev=20, azim=-60):
    """
    配置3D坐标轴的样式，使其更简洁美观。
    借鉴自原始代码。
    """
    ax.set_xlim(x_lim)
    ax.set_ylim(y_lim)
    ax.set_zlim(z_lim)
    ax.view_init(elev=elev, azim=azim)
    ax.set_xticks([])
    ax.set_yticks([])
    ax.set_zticks([])
    ax.xaxis.pane.fill = False
    ax.yaxis.pane.fill = False
    ax.zaxis.pane.fill = False
    ax.xaxis.pane.set_edgecolor('gray')
    ax.yaxis.pane.set_edgecolor('gray')
    ax.zaxis.pane.set_edgecolor('gray')
    ax.xaxis.pane.set_alpha(0.1)
    ax.yaxis.pane.set_alpha(0.1)
    ax.zaxis.pane.set_alpha(0.1)
    ax.set_aspect('auto')


def main(args):
    """主执行函数"""
    # --- 设置 Matplotlib 字体以支持中文 ---
    plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'SimHei', 'Arial Unicode MS']
    plt.rcParams['axes.unicode_minus'] = False

    # --- 1. 检查输入并查找PCD文件 ---
    if not os.path.isdir(args.pcd_dir):
        print(f"错误: 目录 '{args.pcd_dir}' 不存在。")
        return

    pcd_files = sorted([f for f in os.listdir(args.pcd_dir) if f.endswith('.pcd')])
    
    # 兼容处理原始点云也在文件夹中的情况
    if 'original.pcd' in pcd_files:
        pcd_files.remove('original.pcd')

    if len(pcd_files) == 0:
        print(f"错误: 在目录 '{args.pcd_dir}' 中未找到.pcd文件。")
        return
        
    if len(pcd_files) != 8:
        print(f"警告: 在目录中找到了 {len(pcd_files)} 个PCD文件，预期是8个。将全部进行可视化。")

    print(f"在 '{args.pcd_dir}' 中找到 {len(pcd_files)} 个PCD文件进行可视化。")
    
    # --- 2. 加载所有点云并计算全局边界 ---
    all_points_list = []
    pcd_data = {}
    for filename in pcd_files:
        filepath = os.path.join(args.pcd_dir, filename)
        try:
            pcd = o3d.io.read_point_cloud(filepath)
            points_np = np.asarray(pcd.points)
            if points_np.size == 0:
                print(f"警告: 文件 '{filename}' 为空，已跳过。")
                continue
            all_points_list.append(points_np)
            pcd_data[filename] = points_np
        except Exception as e:
            print(f"读取文件 '{filename}' 时出错: {e}")
            continue

    if not all_points_list:
        print("错误: 未能成功加载任何点云数据。")
        return

    # 计算统一的可视化边界，确保所有子图的缩放和视角一致
    all_points_combined = np.vstack(all_points_list)
    x_min, x_max = all_points_combined[:, 0].min(), all_points_combined[:, 0].max()
    y_min, y_max = all_points_combined[:, 1].min(), all_points_combined[:, 1].max()
    z_min, z_max = all_points_combined[:, 2].min(), all_points_combined[:, 2].max()
    
    # 计算中心点和最大范围，增加10%的边距
    max_range = np.array([x_max-x_min, y_max-y_min, z_max-z_min]).max() / 2.0 * 1.1 
    mid_x, mid_y, mid_z = (x_min+x_max)/2.0, (y_min+y_max)/2.0, (z_min+z_max)/2.0

    # --- 3. 创建并绘制 Matplotlib 可视化图 ---
    print("正在创建Matplotlib可视化图...")
    fig = plt.figure(figsize=(18, 18))
    
    # 使用pcd_data.keys()以确保顺序一致
    sorted_filenames = sorted(pcd_data.keys())

    for idx, filename in enumerate(sorted_filenames):
        if idx >= 9: # 最多只绘制9个
            print("警告: PCD文件超过9个，仅可视化前9个。")
            break
        
        ax = fig.add_subplot(3, 3, idx + 1, projection='3d')
        points = pcd_data[filename]
        
        # 使用统一的颜色进行绘制
        ax.scatter(points[:, 0], points[:, 1], points[:, 2], s=2, c='#6495ED')
        
        # 将文件名作为标题
        title = os.path.splitext(filename)[0].replace('_', ' ').title()
        ax.set_title(title, fontsize=14, pad=10)
        
        # 应用统一的坐标轴样式和范围
        setup_3d_axis(
            ax,
            [mid_x - max_range, mid_x + max_range],
            [mid_y - max_range, mid_y + max_range],
            [mid_z - max_range, mid_z + max_range]
        )

    # --- 4. 保存图像 ---
    dir_name = os.path.basename(os.path.normpath(args.pcd_dir))
    fig.suptitle(f'Point Cloud Visualization for: {dir_name}', fontsize=20)
    plt.tight_layout(rect=[0, 0.03, 1, 0.95]) # 调整布局以适应主标题

    output_path = args.output_file if args.output_file else os.path.join(args.pcd_dir, "combined_visualization.png")
    
    plt.savefig(output_path, dpi=200)
    plt.close(fig)
    print(f"可视化图像已成功保存至: {output_path}")


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="从指定目录加载8个PCD文件并生成统一的可视化图像。")
    parser.add_argument(
        '--pcd_dir', 
        type=str, 
        required=True,
        help="包含.pcd文件的目录地址。"
    )
    parser.add_argument(
        '--output_file', 
        type=str, 
        default=None,
        help="输出PNG图像的完整路径。如果未指定，则保存在输入目录中。"
    )
    
    args = parser.parse_args()
    main(args)
name: Nightly
on:
  schedule:
    - cron:  '10 6 * * *'
env:
  OMP_NUM_THREADS: '10'
  MKL_THREADING_LAYER: GNU
jobs:
  linux-x86_64-nightly:
    name: Linux x86_64 nightlies
    runs-on: 4-core-ubuntu
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          fetch-tags: true
      - uses: ./.github/actions/build_conda
        env:
          ANACONDA_API_TOKEN: ${{ secrets.ANACONDA_API_TOKEN }}
        with:
          label: nightly
  linux-x86_64-GPU-CUDA-11-4-4-nightly:
    name: Linux x86_64 GPU nightlies (CUDA 11.4.4)
    runs-on: 4-core-ubuntu-gpu-t4
    env:
      CUDA_ARCHS: "60-real;61-real;62-real;70-real;72-real;75-real;80;86-real"
      FAISS_FLATTEN_CONDA_INCLUDES: "1"
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          fetch-tags: true
      - uses: ./.github/actions/build_conda
        env:
          ANACONDA_API_TOKEN: ${{ secrets.ANACONDA_API_TOKEN }}
        with:
          label: nightly
          cuda: "11.4.4"
  linux-x86_64-GPU-CUDA-12-4-nightly:
    name: Linux x86_64 GPU nightlies (CUDA 12.4.0)
    runs-on: 4-core-ubuntu-gpu-t4
    env:
      CUDA_ARCHS: "70-real;72-real;75-real;80;86-real"
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          fetch-tags: true
      - uses: ./.github/actions/build_conda
        env:
          ANACONDA_API_TOKEN: ${{ secrets.ANACONDA_API_TOKEN }}
        with:
          label: nightly
          cuda: "12.4.0"
  linux-x86_64-GPU-CUVS-CUDA12-4-0-nightly:
    name: Linux x86_64 GPU w/ cuVS nightlies (CUDA 12.4.0)
    runs-on: 4-core-ubuntu-gpu-t4
    env:
      CUDA_ARCHS: "70-real;72-real;75-real;80;86-real"
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          fetch-tags: true
      - uses: ./.github/actions/build_conda
        env:
          ANACONDA_API_TOKEN: ${{ secrets.ANACONDA_API_TOKEN }}
        with:
          label: nightly
          cuvs: "ON"
          cuda: "12.4.0"
  windows-x86_64-nightly:
    name: Windows x86_64 nightlies
    runs-on: windows-2022
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          fetch-tags: true
      - uses: ./.github/actions/build_conda
        env:
          ANACONDA_API_TOKEN: ${{ secrets.ANACONDA_API_TOKEN }}
        with:
          label: nightly
  osx-arm64-nightly:
    name: OSX arm64 nightlies
    runs-on: macos-14
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          fetch-tags: true
      - uses: ./.github/actions/build_conda
        env:
          ANACONDA_API_TOKEN: ${{ secrets.ANACONDA_API_TOKEN }}
        with:
          label: nightly
  linux-arm64-nightly:
    name: Linux arm64 nightlies
    runs-on: 2-core-ubuntu-arm
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          fetch-tags: true
      - uses: ./.github/actions/build_conda
        env:
          ANACONDA_API_TOKEN: ${{ secrets.ANACONDA_API_TOKEN }}
        with:
          label: nightly
  auto-retry:
    name: Auto retry on failure
    if: fromJSON(github.run_attempt) < 2
    runs-on: ubuntu-latest
    steps:
      - name: Start rerun workflow
        env:
          GH_REPO: ${{ github.repository }}
          GH_TOKEN: ${{ github.token }}
          GH_DEBUG: api
        run: |
          gh workflow run retry_build.yml \
            -F run_id=${{ github.run_id }}

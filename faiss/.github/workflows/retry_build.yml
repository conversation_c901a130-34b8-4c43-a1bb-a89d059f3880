name: Retry Build
on:
  workflow_dispatch:
    inputs:
      run_id:
        required: true
jobs:
  rerun-on-failure:
    permissions: write-all
    runs-on: ubuntu-latest
    steps:
      - name: rerun ${{ inputs.run_id }}
        env:
          GH_REPO: ${{ github.repository }}
          GH_TOKEN: ${{ github.token }}
          GH_DEBUG: api
        run: |
          # status can be one of "queued", "in_progress", "completed", "waiting", "requested", "pending"
          # https://docs.github.com/en/rest/checks/runs
          # while not completed, sleep for 10 minutes
          while gh run view ${{ inputs.run_id }} --json status | grep -v completed
          do
            echo Workflow in progress - sleeping for 10 minutes then checking again
            sleep 10m
          done

          # Only retry if there are failed jobs
          if gh run view ${{ inputs.run_id }} --exit-status; then
            echo Workflow succeeded - no retry necessary.
          else
            echo Workflow failed - initiating retry.
            gh run rerun ${{ inputs.run_id }} --failed
          fi

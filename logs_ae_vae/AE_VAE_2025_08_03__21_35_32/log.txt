[2025-08-03 21:35:32,110::train::INFO] Namespace(latent_dim=256, model_type='vae', resume=None, use_vae=True, kl_weight=0.001, num_steps=1000, beta_1=0.0001, beta_T=0.02, sched_mode='cosine', learn_sigma_diffusion=True, residual_within_block_diffusion=True, time_emb_dim_diffusion=128, lambda_vlb_diffusion=0.001, dataset_path='/home/<USER>/llm/3DAD/data/Real3D-AD-PCD', categories=['airplane'], scale_mode='shape_bbox', train_batch_size=4, val_batch_size=4, num_points=2048, sampling_method='hybrid', rotate=False, precomputed_defect_path='./defect_generations_strong', lr=0.0001, weight_decay=0.0, max_grad_norm=1.0, use_ema=True, ema_decay=0.9999, seed=2024, logging=True, log_root='./logs_ae_vae', device='cuda', max_iters=50000, val_freq=2000, tag=None, num_workers=4, warmup_iters=2500, num_val_batches=4, num_inspect_batches=1, num_inspect_pointclouds=2, ret_traj=False, sample_steps_decode=None, flexibility=0.0, metrics_in_normalized_space=True, pretrained_vae_path='/home/<USER>/llm/3DAD/diffusion-point-cloud/logs_vae/VAE_2025_07_28__13_32_48/model_best.pt', freeze_encoder_after=True, num_defect_types=3, label_emb_dim=8)
[2025-08-03 21:36:09,927::train::INFO] 训练集 (使用 PrecomputedDefectDataset 统一处理): 32 个样本
[2025-08-03 21:36:09,928::train::INFO] 验证集 (使用 ValidationDataset 统一处理): 4 个样本
[2025-08-03 21:36:09,929::train::INFO] Building model...
[2025-08-03 21:36:10,139::train::ERROR] Failed to load pretrained VAE encoder: Weights only load failed. This file can still be loaded, to do so you have two options, [1mdo those steps only if you trust the source of the checkpoint[0m. 
	(1) In PyTorch 2.6, we changed the default value of the `weights_only` argument in `torch.load` from `False` to `True`. Re-running `torch.load` with `weights_only` set to `False` will likely succeed, but it can result in arbitrary code execution. Do it only if you got the file from a trusted source.
	(2) Alternatively, to load with `weights_only=True` please check the recommended steps in the following error message.
	WeightsUnpickler error: Unsupported global: GLOBAL argparse.Namespace was not an allowed global by default. Please use `torch.serialization.add_safe_globals([Namespace])` or the `torch.serialization.safe_globals([Namespace])` context manager to allowlist this global if you trust this class/function.

Check the documentation of torch.load to learn more about types accepted by default with weights_only https://pytorch.org/docs/stable/generated/torch.load.html.
[2025-08-03 21:36:10,140::train::INFO] AutoEncoder(
  (encoder): PointTransformerV3Encoder(
    (embedding): Sequential(
      (0): Conv1d(3, 128, kernel_size=(1,), stride=(1,))
      (1): BatchNorm1d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (2): ReLU()
      (3): Conv1d(128, 128, kernel_size=(1,), stride=(1,))
      (4): BatchNorm1d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (5): ReLU()
    )
    (sa1): TransformerBlock(
      (q_conv): Conv1d(128, 128, kernel_size=(1,), stride=(1,), bias=False)
      (k_conv): Conv1d(128, 128, kernel_size=(1,), stride=(1,), bias=False)
      (v_conv): Conv1d(128, 128, kernel_size=(1,), stride=(1,), bias=False)
      (attn_norm): BatchNorm1d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (ff): Sequential(
        (0): Conv1d(128, 256, kernel_size=(1,), stride=(1,))
        (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU()
        (3): Conv1d(256, 128, kernel_size=(1,), stride=(1,))
        (4): BatchNorm1d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      )
      (shortcut): Identity()
    )
    (sa2): TransformerBlock(
      (q_conv): Conv1d(128, 256, kernel_size=(1,), stride=(1,), bias=False)
      (k_conv): Conv1d(128, 256, kernel_size=(1,), stride=(1,), bias=False)
      (v_conv): Conv1d(128, 256, kernel_size=(1,), stride=(1,), bias=False)
      (attn_norm): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (ff): Sequential(
        (0): Conv1d(256, 512, kernel_size=(1,), stride=(1,))
        (1): BatchNorm1d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU()
        (3): Conv1d(512, 256, kernel_size=(1,), stride=(1,))
        (4): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      )
      (shortcut): Sequential(
        (0): Conv1d(128, 256, kernel_size=(1,), stride=(1,))
        (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      )
    )
    (sa3): TransformerBlock(
      (q_conv): Conv1d(256, 512, kernel_size=(1,), stride=(1,), bias=False)
      (k_conv): Conv1d(256, 512, kernel_size=(1,), stride=(1,), bias=False)
      (v_conv): Conv1d(256, 512, kernel_size=(1,), stride=(1,), bias=False)
      (attn_norm): BatchNorm1d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (ff): Sequential(
        (0): Conv1d(512, 1024, kernel_size=(1,), stride=(1,))
        (1): BatchNorm1d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU()
        (3): Conv1d(1024, 512, kernel_size=(1,), stride=(1,))
        (4): BatchNorm1d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      )
      (shortcut): Sequential(
        (0): Conv1d(256, 512, kernel_size=(1,), stride=(1,))
        (1): BatchNorm1d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      )
    )
    (global_conv): Sequential(
      (0): Conv1d(512, 1024, kernel_size=(1,), stride=(1,))
      (1): BatchNorm1d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (2): ReLU()
    )
    (fc_mean): Sequential(
      (0): Linear(in_features=1024, out_features=512, bias=True)
      (1): BatchNorm1d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (2): ReLU()
      (3): Linear(in_features=512, out_features=256, bias=True)
      (4): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (5): ReLU()
      (6): Linear(in_features=256, out_features=256, bias=True)
    )
    (fc_var): Sequential(
      (0): Linear(in_features=1024, out_features=512, bias=True)
      (1): BatchNorm1d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (2): ReLU()
      (3): Linear(in_features=512, out_features=256, bias=True)
      (4): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (5): ReLU()
      (6): Linear(in_features=256, out_features=256, bias=True)
    )
  )
  (label_emb): Embedding(3, 8)
  (diffusion_decoder): DiffusionPoint(
    (net): PointwiseNet(
      (act): SiLU()
      (time_mlp): SinusoidalPositionEmbeddings()
      (point_embedding): Sequential(
        (0): Linear(in_features=3, out_features=64, bias=True)
        (1): LayerNorm((64,), eps=1e-05, elementwise_affine=True)
        (2): SiLU()
        (3): Linear(in_features=64, out_features=128, bias=True)
        (4): LayerNorm((128,), eps=1e-05, elementwise_affine=True)
        (5): SiLU()
      )
      (context_embedding_mlp): Sequential(
        (0): Linear(in_features=392, out_features=128, bias=True)
        (1): LayerNorm((128,), eps=1e-05, elementwise_affine=True)
        (2): SiLU()
        (3): Linear(in_features=128, out_features=128, bias=True)
        (4): LayerNorm((128,), eps=1e-05, elementwise_affine=True)
        (5): SiLU()
      )
      (local_attention_blocks): ModuleList(
        (0-1): 2 x SelfAttentionBlock(
          (norm): LayerNorm((128,), eps=1e-05, elementwise_affine=True)
          (attention): MultiheadAttention(
            (out_proj): NonDynamicallyQuantizableLinear(in_features=128, out_features=128, bias=True)
          )
          (ff): Sequential(
            (0): Linear(in_features=128, out_features=512, bias=True)
            (1): GELU(approximate='none')
            (2): Linear(in_features=512, out_features=128, bias=True)
          )
          (ff_norm): LayerNorm((128,), eps=1e-05, elementwise_affine=True)
        )
      )
      (global_attention_blocks): ModuleList(
        (0-1): 2 x CrossAttentionBlock(
          (norm_q): LayerNorm((128,), eps=1e-05, elementwise_affine=True)
          (norm_kv): LayerNorm((128,), eps=1e-05, elementwise_affine=True)
          (attention): MultiheadAttention(
            (out_proj): NonDynamicallyQuantizableLinear(in_features=128, out_features=128, bias=True)
          )
          (ff): Sequential(
            (0): Linear(in_features=128, out_features=512, bias=True)
            (1): GELU(approximate='none')
            (2): Linear(in_features=512, out_features=128, bias=True)
          )
          (ff_norm): LayerNorm((128,), eps=1e-05, elementwise_affine=True)
        )
      )
      (output_layers): Sequential(
        (0): Linear(in_features=128, out_features=128, bias=True)
        (1): LayerNorm((128,), eps=1e-05, elementwise_affine=True)
        (2): SiLU()
        (3): Linear(in_features=128, out_features=64, bias=True)
        (4): LayerNorm((64,), eps=1e-05, elementwise_affine=True)
        (5): SiLU()
        (6): Linear(in_features=64, out_features=6, bias=True)
      )
    )
    (var_sched): VarianceSchedule()
  )
)
[2025-08-03 21:36:10,146::train::INFO] EMA initialized with decay=0.9999
[2025-08-03 21:36:10,146::train::INFO] Encoder frozen as per flag.
[2025-08-03 21:36:10,147::train::INFO] Setting up learning rate schedule: 2500 warmup iters, then cosine decay.
[2025-08-03 21:36:10,147::train::INFO] Start training...
[2025-08-03 21:36:10,517::train::WARNING] 跳过迭代 1，数据加载器返回空批次。
[2025-08-03 21:36:10,521::train::WARNING] 跳过迭代 2，数据加载器返回空批次。
[2025-08-03 21:36:10,521::train::WARNING] 跳过迭代 3，数据加载器返回空批次。
[2025-08-03 21:36:10,542::train::WARNING] 跳过迭代 4，数据加载器返回空批次。
[2025-08-03 21:36:10,752::train::WARNING] 跳过迭代 5，数据加载器返回空批次。
[2025-08-03 21:36:10,755::train::WARNING] 跳过迭代 6，数据加载器返回空批次。
[2025-08-03 21:36:10,757::train::WARNING] 跳过迭代 7，数据加载器返回空批次。
[2025-08-03 21:36:10,774::train::WARNING] 跳过迭代 8，数据加载器返回空批次。
[2025-08-03 21:36:11,216::train::WARNING] 跳过迭代 9，数据加载器返回空批次。
[2025-08-03 21:36:11,220::train::WARNING] 跳过迭代 10，数据加载器返回空批次。
[2025-08-03 21:36:11,221::train::WARNING] 跳过迭代 11，数据加载器返回空批次。
[2025-08-03 21:36:11,222::train::WARNING] 跳过迭代 12，数据加载器返回空批次。
[2025-08-03 21:36:11,473::train::WARNING] 跳过迭代 13，数据加载器返回空批次。
[2025-08-03 21:36:11,474::train::WARNING] 跳过迭代 14，数据加载器返回空批次。
[2025-08-03 21:36:11,476::train::WARNING] 跳过迭代 15，数据加载器返回空批次。
[2025-08-03 21:36:11,477::train::WARNING] 跳过迭代 16，数据加载器返回空批次。
[2025-08-03 21:36:11,909::train::WARNING] 跳过迭代 17，数据加载器返回空批次。
[2025-08-03 21:36:11,919::train::WARNING] 跳过迭代 18，数据加载器返回空批次。
[2025-08-03 21:36:11,921::train::WARNING] 跳过迭代 19，数据加载器返回空批次。
[2025-08-03 21:36:11,923::train::WARNING] 跳过迭代 20，数据加载器返回空批次。
[2025-08-03 21:36:12,141::train::WARNING] 跳过迭代 21，数据加载器返回空批次。
[2025-08-03 21:36:12,156::train::WARNING] 跳过迭代 22，数据加载器返回空批次。
[2025-08-03 21:36:12,157::train::WARNING] 跳过迭代 23，数据加载器返回空批次。
[2025-08-03 21:36:12,162::train::WARNING] 跳过迭代 24，数据加载器返回空批次。
[2025-08-03 21:36:12,605::train::WARNING] 跳过迭代 25，数据加载器返回空批次。
[2025-08-03 21:36:12,624::train::WARNING] 跳过迭代 26，数据加载器返回空批次。
[2025-08-03 21:36:12,626::train::WARNING] 跳过迭代 27，数据加载器返回空批次。
[2025-08-03 21:36:12,627::train::WARNING] 跳过迭代 28，数据加载器返回空批次。
[2025-08-03 21:36:12,841::train::WARNING] 跳过迭代 29，数据加载器返回空批次。
[2025-08-03 21:36:12,886::train::WARNING] 跳过迭代 30，数据加载器返回空批次。
[2025-08-03 21:36:12,887::train::WARNING] 跳过迭代 31，数据加载器返回空批次。
[2025-08-03 21:36:12,889::train::WARNING] 跳过迭代 32，数据加载器返回空批次。
[2025-08-03 21:36:13,355::train::WARNING] 跳过迭代 33，数据加载器返回空批次。
[2025-08-03 21:36:13,359::train::WARNING] 跳过迭代 34，数据加载器返回空批次。
[2025-08-03 21:36:13,360::train::WARNING] 跳过迭代 35，数据加载器返回空批次。
[2025-08-03 21:36:13,361::train::WARNING] 跳过迭代 36，数据加载器返回空批次。
[2025-08-03 21:36:13,600::train::WARNING] 跳过迭代 37，数据加载器返回空批次。
[2025-08-03 21:36:13,602::train::WARNING] 跳过迭代 38，数据加载器返回空批次。
[2025-08-03 21:36:13,603::train::WARNING] 跳过迭代 39，数据加载器返回空批次。
[2025-08-03 21:36:13,605::train::WARNING] 跳过迭代 40，数据加载器返回空批次。

[2025-08-03 22:06:15,074::train::INFO] Namespace(latent_dim=256, model_type='vae', resume=None, use_vae=True, kl_weight=0.001, num_steps=1000, beta_1=0.0001, beta_T=0.02, sched_mode='cosine', learn_sigma_diffusion=True, residual_within_block_diffusion=True, time_emb_dim_diffusion=128, lambda_vlb_diffusion=0.001, dataset_path='/home/<USER>/llm/3DAD/data/Real3D-AD-PCD', categories=['airplane'], scale_mode='shape_bbox', train_batch_size=4, val_batch_size=4, num_points=2048, sampling_method='hybrid', rotate=False, precomputed_defect_path='./defect_generations_strong', lr=0.0001, weight_decay=0.0, max_grad_norm=1.0, use_ema=True, ema_decay=0.9999, seed=2024, logging=True, log_root='./logs_ae_vae', device='cuda', max_iters=50000, val_freq=2000, tag=None, num_workers=4, warmup_iters=2500, num_val_batches=4, num_inspect_batches=1, num_inspect_pointclouds=2, ret_traj=False, sample_steps_decode=None, flexibility=0.0, metrics_in_normalized_space=True, pretrained_vae_path='/home/<USER>/llm/3DAD/diffusion-point-cloud/logs_vae/VAE_2025_07_28__13_32_48/model_best.pt', freeze_encoder_after=True, num_defect_types=3, label_emb_dim=8)
[2025-08-03 22:06:15,336::train::INFO] 训练集 (使用 PrecomputedDefectDataset 统一处理): 32 个样本
[2025-08-03 22:06:15,336::train::INFO] 验证集 (使用 ValidationDataset 统一处理): 4 个样本
[2025-08-03 22:06:15,336::train::INFO] Building model...
[2025-08-03 22:06:17,040::train::INFO] Loaded encoder weights from pretrained VAE /home/<USER>/llm/3DAD/diffusion-point-cloud/logs_vae/VAE_2025_07_28__13_32_48/model_best.pt
[2025-08-03 22:06:17,041::train::INFO] OptimizedModule(
  (_orig_mod): AutoEncoder(
    (encoder): PointTransformerV3Encoder(
      (embedding): Sequential(
        (0): Conv1d(3, 128, kernel_size=(1,), stride=(1,))
        (1): BatchNorm1d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU()
        (3): Conv1d(128, 128, kernel_size=(1,), stride=(1,))
        (4): BatchNorm1d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (5): ReLU()
      )
      (sa1): TransformerBlock(
        (q_conv): Conv1d(128, 128, kernel_size=(1,), stride=(1,), bias=False)
        (k_conv): Conv1d(128, 128, kernel_size=(1,), stride=(1,), bias=False)
        (v_conv): Conv1d(128, 128, kernel_size=(1,), stride=(1,), bias=False)
        (attn_norm): BatchNorm1d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (ff): Sequential(
          (0): Conv1d(128, 256, kernel_size=(1,), stride=(1,))
          (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU()
          (3): Conv1d(256, 128, kernel_size=(1,), stride=(1,))
          (4): BatchNorm1d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
        (shortcut): Identity()
      )
      (sa2): TransformerBlock(
        (q_conv): Conv1d(128, 256, kernel_size=(1,), stride=(1,), bias=False)
        (k_conv): Conv1d(128, 256, kernel_size=(1,), stride=(1,), bias=False)
        (v_conv): Conv1d(128, 256, kernel_size=(1,), stride=(1,), bias=False)
        (attn_norm): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (ff): Sequential(
          (0): Conv1d(256, 512, kernel_size=(1,), stride=(1,))
          (1): BatchNorm1d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU()
          (3): Conv1d(512, 256, kernel_size=(1,), stride=(1,))
          (4): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
        (shortcut): Sequential(
          (0): Conv1d(128, 256, kernel_size=(1,), stride=(1,))
          (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
      )
      (sa3): TransformerBlock(
        (q_conv): Conv1d(256, 512, kernel_size=(1,), stride=(1,), bias=False)
        (k_conv): Conv1d(256, 512, kernel_size=(1,), stride=(1,), bias=False)
        (v_conv): Conv1d(256, 512, kernel_size=(1,), stride=(1,), bias=False)
        (attn_norm): BatchNorm1d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (ff): Sequential(
          (0): Conv1d(512, 1024, kernel_size=(1,), stride=(1,))
          (1): BatchNorm1d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU()
          (3): Conv1d(1024, 512, kernel_size=(1,), stride=(1,))
          (4): BatchNorm1d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
        (shortcut): Sequential(
          (0): Conv1d(256, 512, kernel_size=(1,), stride=(1,))
          (1): BatchNorm1d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
      )
      (global_conv): Sequential(
        (0): Conv1d(512, 1024, kernel_size=(1,), stride=(1,))
        (1): BatchNorm1d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU()
      )
      (fc_mean): Sequential(
        (0): Linear(in_features=1024, out_features=512, bias=True)
        (1): BatchNorm1d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU()
        (3): Linear(in_features=512, out_features=256, bias=True)
        (4): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (5): ReLU()
        (6): Linear(in_features=256, out_features=256, bias=True)
      )
      (fc_var): Sequential(
        (0): Linear(in_features=1024, out_features=512, bias=True)
        (1): BatchNorm1d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU()
        (3): Linear(in_features=512, out_features=256, bias=True)
        (4): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (5): ReLU()
        (6): Linear(in_features=256, out_features=256, bias=True)
      )
    )
    (label_emb): Embedding(3, 8)
    (diffusion_decoder): DiffusionPoint(
      (net): PointwiseNet(
        (act): SiLU()
        (time_mlp): SinusoidalPositionEmbeddings()
        (point_embedding): Sequential(
          (0): Linear(in_features=3, out_features=64, bias=True)
          (1): LayerNorm((64,), eps=1e-05, elementwise_affine=True)
          (2): SiLU()
          (3): Linear(in_features=64, out_features=128, bias=True)
          (4): LayerNorm((128,), eps=1e-05, elementwise_affine=True)
          (5): SiLU()
        )
        (context_embedding_mlp): Sequential(
          (0): Linear(in_features=392, out_features=128, bias=True)
          (1): LayerNorm((128,), eps=1e-05, elementwise_affine=True)
          (2): SiLU()
          (3): Linear(in_features=128, out_features=128, bias=True)
          (4): LayerNorm((128,), eps=1e-05, elementwise_affine=True)
          (5): SiLU()
        )
        (local_attention_blocks): ModuleList(
          (0-1): 2 x SelfAttentionBlock(
            (norm): LayerNorm((128,), eps=1e-05, elementwise_affine=True)
            (attention): MultiheadAttention(
              (out_proj): NonDynamicallyQuantizableLinear(in_features=128, out_features=128, bias=True)
            )
            (ff): Sequential(
              (0): Linear(in_features=128, out_features=512, bias=True)
              (1): GELU(approximate='none')
              (2): Linear(in_features=512, out_features=128, bias=True)
            )
            (ff_norm): LayerNorm((128,), eps=1e-05, elementwise_affine=True)
          )
        )
        (global_attention_blocks): ModuleList(
          (0-1): 2 x CrossAttentionBlock(
            (norm_q): LayerNorm((128,), eps=1e-05, elementwise_affine=True)
            (norm_kv): LayerNorm((128,), eps=1e-05, elementwise_affine=True)
            (attention): MultiheadAttention(
              (out_proj): NonDynamicallyQuantizableLinear(in_features=128, out_features=128, bias=True)
            )
            (ff): Sequential(
              (0): Linear(in_features=128, out_features=512, bias=True)
              (1): GELU(approximate='none')
              (2): Linear(in_features=512, out_features=128, bias=True)
            )
            (ff_norm): LayerNorm((128,), eps=1e-05, elementwise_affine=True)
          )
        )
        (output_layers): Sequential(
          (0): Linear(in_features=128, out_features=128, bias=True)
          (1): LayerNorm((128,), eps=1e-05, elementwise_affine=True)
          (2): SiLU()
          (3): Linear(in_features=128, out_features=64, bias=True)
          (4): LayerNorm((64,), eps=1e-05, elementwise_affine=True)
          (5): SiLU()
          (6): Linear(in_features=64, out_features=6, bias=True)
        )
      )
      (var_sched): VarianceSchedule()
    )
  )
)
[2025-08-03 22:06:17,046::train::INFO] EMA initialized with decay=0.9999
[2025-08-03 22:06:17,046::train::INFO] Encoder frozen as per flag.
[2025-08-03 22:06:17,047::train::INFO] Setting up learning rate schedule: 2500 warmup iters, then cosine decay.
[2025-08-03 22:06:17,047::train::INFO] Start training...
[2025-08-03 22:09:37,863::train::ERROR] An error occurred during training: CUDA out of memory. Tried to allocate 1024.00 MiB. GPU 0 has a total capacity of 23.64 GiB of which 911.31 MiB is free. Process 3415043 has 7.68 GiB memory in use. Process 3772411 has 7.68 GiB memory in use. Process 3878372 has 3.55 GiB memory in use. Process 146886 has 424.00 MiB memory in use. Process 149493 has 424.00 MiB memory in use. Process 149966 has 424.00 MiB memory in use. Including non-PyTorch memory, this process has 2.56 GiB memory in use. Of the allocated memory 1.12 GiB is allocated by PyTorch, and 1014.09 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
Traceback (most recent call last):
  File "/home/<USER>/llm/3DAD/diffusion-point-cloud/train_ae.py", line 480, in <module>
    train(it)
  File "/home/<USER>/llm/3DAD/diffusion-point-cloud/train_ae.py", line 306, in train
    latent_z, _ = model.encoder(pcs_norm)
                  ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/llm/3DAD/diffusion-point-cloud/.venv/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1739, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/llm/3DAD/diffusion-point-cloud/.venv/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1750, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/llm/3DAD/diffusion-point-cloud/models/encoders/pointnet.py", line 84, in forward
    x = self.sa2(x)  # [B, 256, N]
        ^^^^^^^^^^^
  File "/home/<USER>/llm/3DAD/diffusion-point-cloud/.venv/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1739, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/llm/3DAD/diffusion-point-cloud/.venv/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1750, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/llm/3DAD/diffusion-point-cloud/models/encoders/pointnet.py", line 165, in forward
    attn = torch.matmul(q, k) / (self.head_dim ** 0.5)  # [B, H, N, N]
           ~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~
torch.OutOfMemoryError: CUDA out of memory. Tried to allocate 1024.00 MiB. GPU 0 has a total capacity of 23.64 GiB of which 911.31 MiB is free. Process 3415043 has 7.68 GiB memory in use. Process 3772411 has 7.68 GiB memory in use. Process 3878372 has 3.55 GiB memory in use. Process 146886 has 424.00 MiB memory in use. Process 149493 has 424.00 MiB memory in use. Process 149966 has 424.00 MiB memory in use. Including non-PyTorch memory, this process has 2.56 GiB memory in use. Of the allocated memory 1.12 GiB is allocated by PyTorch, and 1014.09 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
[2025-08-03 22:09:37,866::train::INFO] Training finished or terminated.

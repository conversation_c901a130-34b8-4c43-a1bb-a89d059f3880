[2025-08-03 19:44:34,464::train::INFO] Namespace(latent_dim=256, model_type='vae', resume=None, use_vae=True, kl_weight=0.001, num_steps=1000, beta_1=0.0001, beta_T=0.02, sched_mode='cosine', learn_sigma_diffusion=True, residual_within_block_diffusion=True, time_emb_dim_diffusion=128, lambda_vlb_diffusion=0.001, dataset_path='/home/<USER>/llm/3DAD/data/Real3D-AD-PCD', categories=['airplane'], scale_mode='shape_bbox', train_batch_size=4, val_batch_size=4, num_points=2048, sampling_method='hybrid', rotate=False, precomputed_defect_path='./defect_generations_strong', lr=0.0001, weight_decay=0.0, max_grad_norm=1.0, use_ema=True, ema_decay=0.9999, seed=2024, logging=True, log_root='./logs_ae_vae', device='cuda', max_iters=50000, val_freq=2000, tag=None, num_workers=4, warmup_iters=2500, num_val_batches=4, num_inspect_batches=1, num_inspect_pointclouds=2, ret_traj=False, sample_steps_decode=None, flexibility=0.0, metrics_in_normalized_space=True, pretrained_vae_path='/home/<USER>/llm/3DAD/diffusion-point-cloud/logs_vae/VAE_2025_07_28__13_32_48/model_best.pt', freeze_encoder_after=True, num_defect_types=3, label_emb_dim=8)
[2025-08-03 19:44:34,466::train::INFO] Loading datasets...
[2025-08-03 19:46:44,661::train::INFO] 训练集 (从预计算目录加载): 32 个样本
[2025-08-03 19:46:44,662::train::INFO] 验证集 (原始正常数据): 4 个样本
[2025-08-03 19:46:44,665::train::INFO] Building model...
[2025-08-03 19:46:45,013::train::ERROR] Failed to load pretrained VAE encoder: Weights only load failed. This file can still be loaded, to do so you have two options, [1mdo those steps only if you trust the source of the checkpoint[0m. 
	(1) In PyTorch 2.6, we changed the default value of the `weights_only` argument in `torch.load` from `False` to `True`. Re-running `torch.load` with `weights_only` set to `False` will likely succeed, but it can result in arbitrary code execution. Do it only if you got the file from a trusted source.
	(2) Alternatively, to load with `weights_only=True` please check the recommended steps in the following error message.
	WeightsUnpickler error: Unsupported global: GLOBAL argparse.Namespace was not an allowed global by default. Please use `torch.serialization.add_safe_globals([Namespace])` or the `torch.serialization.safe_globals([Namespace])` context manager to allowlist this global if you trust this class/function.

Check the documentation of torch.load to learn more about types accepted by default with weights_only https://pytorch.org/docs/stable/generated/torch.load.html.
[2025-08-03 19:46:45,015::train::INFO] AutoEncoder(
  (encoder): PointTransformerV3Encoder(
    (embedding): Sequential(
      (0): Conv1d(3, 128, kernel_size=(1,), stride=(1,))
      (1): BatchNorm1d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (2): ReLU()
      (3): Conv1d(128, 128, kernel_size=(1,), stride=(1,))
      (4): BatchNorm1d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (5): ReLU()
    )
    (sa1): TransformerBlock(
      (q_conv): Conv1d(128, 128, kernel_size=(1,), stride=(1,), bias=False)
      (k_conv): Conv1d(128, 128, kernel_size=(1,), stride=(1,), bias=False)
      (v_conv): Conv1d(128, 128, kernel_size=(1,), stride=(1,), bias=False)
      (attn_norm): BatchNorm1d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (ff): Sequential(
        (0): Conv1d(128, 256, kernel_size=(1,), stride=(1,))
        (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU()
        (3): Conv1d(256, 128, kernel_size=(1,), stride=(1,))
        (4): BatchNorm1d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      )
      (shortcut): Identity()
    )
    (sa2): TransformerBlock(
      (q_conv): Conv1d(128, 256, kernel_size=(1,), stride=(1,), bias=False)
      (k_conv): Conv1d(128, 256, kernel_size=(1,), stride=(1,), bias=False)
      (v_conv): Conv1d(128, 256, kernel_size=(1,), stride=(1,), bias=False)
      (attn_norm): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (ff): Sequential(
        (0): Conv1d(256, 512, kernel_size=(1,), stride=(1,))
        (1): BatchNorm1d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU()
        (3): Conv1d(512, 256, kernel_size=(1,), stride=(1,))
        (4): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      )
      (shortcut): Sequential(
        (0): Conv1d(128, 256, kernel_size=(1,), stride=(1,))
        (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      )
    )
    (sa3): TransformerBlock(
      (q_conv): Conv1d(256, 512, kernel_size=(1,), stride=(1,), bias=False)
      (k_conv): Conv1d(256, 512, kernel_size=(1,), stride=(1,), bias=False)
      (v_conv): Conv1d(256, 512, kernel_size=(1,), stride=(1,), bias=False)
      (attn_norm): BatchNorm1d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (ff): Sequential(
        (0): Conv1d(512, 1024, kernel_size=(1,), stride=(1,))
        (1): BatchNorm1d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU()
        (3): Conv1d(1024, 512, kernel_size=(1,), stride=(1,))
        (4): BatchNorm1d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      )
      (shortcut): Sequential(
        (0): Conv1d(256, 512, kernel_size=(1,), stride=(1,))
        (1): BatchNorm1d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      )
    )
    (global_conv): Sequential(
      (0): Conv1d(512, 1024, kernel_size=(1,), stride=(1,))
      (1): BatchNorm1d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (2): ReLU()
    )
    (fc_mean): Sequential(
      (0): Linear(in_features=1024, out_features=512, bias=True)
      (1): BatchNorm1d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (2): ReLU()
      (3): Linear(in_features=512, out_features=256, bias=True)
      (4): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (5): ReLU()
      (6): Linear(in_features=256, out_features=256, bias=True)
    )
    (fc_var): Sequential(
      (0): Linear(in_features=1024, out_features=512, bias=True)
      (1): BatchNorm1d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (2): ReLU()
      (3): Linear(in_features=512, out_features=256, bias=True)
      (4): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (5): ReLU()
      (6): Linear(in_features=256, out_features=256, bias=True)
    )
  )
  (label_emb): Embedding(3, 8)
  (diffusion_decoder): DiffusionPoint(
    (net): PointwiseNet(
      (act): SiLU()
      (time_mlp): SinusoidalPositionEmbeddings()
      (point_embedding): Sequential(
        (0): Linear(in_features=3, out_features=64, bias=True)
        (1): LayerNorm((64,), eps=1e-05, elementwise_affine=True)
        (2): SiLU()
        (3): Linear(in_features=64, out_features=128, bias=True)
        (4): LayerNorm((128,), eps=1e-05, elementwise_affine=True)
        (5): SiLU()
      )
      (context_embedding_mlp): Sequential(
        (0): Linear(in_features=392, out_features=128, bias=True)
        (1): LayerNorm((128,), eps=1e-05, elementwise_affine=True)
        (2): SiLU()
        (3): Linear(in_features=128, out_features=128, bias=True)
        (4): LayerNorm((128,), eps=1e-05, elementwise_affine=True)
        (5): SiLU()
      )
      (local_attention_blocks): ModuleList(
        (0-1): 2 x SelfAttentionBlock(
          (norm): LayerNorm((128,), eps=1e-05, elementwise_affine=True)
          (attention): MultiheadAttention(
            (out_proj): NonDynamicallyQuantizableLinear(in_features=128, out_features=128, bias=True)
          )
          (ff): Sequential(
            (0): Linear(in_features=128, out_features=512, bias=True)
            (1): GELU(approximate='none')
            (2): Linear(in_features=512, out_features=128, bias=True)
          )
          (ff_norm): LayerNorm((128,), eps=1e-05, elementwise_affine=True)
        )
      )
      (global_attention_blocks): ModuleList(
        (0-1): 2 x CrossAttentionBlock(
          (norm_q): LayerNorm((128,), eps=1e-05, elementwise_affine=True)
          (norm_kv): LayerNorm((128,), eps=1e-05, elementwise_affine=True)
          (attention): MultiheadAttention(
            (out_proj): NonDynamicallyQuantizableLinear(in_features=128, out_features=128, bias=True)
          )
          (ff): Sequential(
            (0): Linear(in_features=128, out_features=512, bias=True)
            (1): GELU(approximate='none')
            (2): Linear(in_features=512, out_features=128, bias=True)
          )
          (ff_norm): LayerNorm((128,), eps=1e-05, elementwise_affine=True)
        )
      )
      (output_layers): Sequential(
        (0): Linear(in_features=128, out_features=128, bias=True)
        (1): LayerNorm((128,), eps=1e-05, elementwise_affine=True)
        (2): SiLU()
        (3): Linear(in_features=128, out_features=64, bias=True)
        (4): LayerNorm((64,), eps=1e-05, elementwise_affine=True)
        (5): SiLU()
        (6): Linear(in_features=64, out_features=6, bias=True)
      )
    )
    (var_sched): VarianceSchedule()
  )
)
[2025-08-03 19:46:45,020::train::INFO] EMA initialized with decay=0.9999
[2025-08-03 19:46:45,021::train::INFO] Encoder frozen as per flag.
[2025-08-03 19:46:45,022::train::INFO] Setting up learning rate schedule: 2500 warmup iters, then cosine decay.
[2025-08-03 19:46:45,023::train::INFO] Start training...
[2025-08-03 19:46:52,207::train::INFO] [Train] Iter 000050 | Loss 0.908555 | Grad Norm 3.8289 | LR 2.000980e-06
[2025-08-03 19:46:57,980::train::INFO] [Train] Iter 000100 | Loss 0.719226 | Grad Norm 2.6584 | LR 4.000960e-06
[2025-08-03 19:47:03,477::train::INFO] [Train] Iter 000150 | Loss 0.498795 | Grad Norm 2.1027 | LR 6.000940e-06
[2025-08-03 19:47:09,024::train::INFO] Norms - latent_z: 5.5026, label_vec: 2.7123
[2025-08-03 19:47:09,061::train::INFO] [Train] Iter 000200 | Loss 0.426273 | Grad Norm 1.8014 | LR 8.000920e-06
[2025-08-03 19:47:14,957::train::INFO] [Train] Iter 000250 | Loss 0.284561 | Grad Norm 0.7658 | LR 1.000090e-05
[2025-08-03 19:47:20,616::train::INFO] [Train] Iter 000300 | Loss 0.263811 | Grad Norm 0.7122 | LR 1.200088e-05
[2025-08-03 19:47:26,313::train::INFO] [Train] Iter 000350 | Loss 0.251204 | Grad Norm 0.7324 | LR 1.400086e-05
[2025-08-03 19:47:32,025::train::INFO] Norms - latent_z: 6.3154, label_vec: 2.4002
[2025-08-03 19:47:32,067::train::INFO] [Train] Iter 000400 | Loss 0.137205 | Grad Norm 0.4438 | LR 1.600084e-05
[2025-08-03 19:47:38,274::train::INFO] [Train] Iter 000450 | Loss 0.186129 | Grad Norm 0.5259 | LR 1.800082e-05
[2025-08-03 19:47:44,159::train::INFO] [Train] Iter 000500 | Loss 0.109626 | Grad Norm 0.7625 | LR 2.000080e-05
[2025-08-03 19:47:49,761::train::INFO] [Train] Iter 000550 | Loss 0.125623 | Grad Norm 0.3004 | LR 2.200078e-05
[2025-08-03 19:47:55,464::train::INFO] Norms - latent_z: 5.6715, label_vec: 2.5560
[2025-08-03 19:47:55,510::train::INFO] [Train] Iter 000600 | Loss 0.164977 | Grad Norm 0.1856 | LR 2.400076e-05
[2025-08-03 19:48:01,635::train::INFO] [Train] Iter 000650 | Loss 0.289055 | Grad Norm 0.5690 | LR 2.600074e-05
[2025-08-03 19:48:07,384::train::INFO] [Train] Iter 000700 | Loss 0.184824 | Grad Norm 0.4802 | LR 2.800072e-05
[2025-08-03 19:48:12,992::train::INFO] [Train] Iter 000750 | Loss 0.192407 | Grad Norm 0.2447 | LR 3.000070e-05
[2025-08-03 19:48:18,514::train::INFO] Norms - latent_z: 6.5565, label_vec: 2.2439
[2025-08-03 19:48:18,549::train::INFO] [Train] Iter 000800 | Loss 0.225038 | Grad Norm 0.2409 | LR 3.200068e-05
[2025-08-03 19:48:24,340::train::INFO] [Train] Iter 000850 | Loss 0.172452 | Grad Norm 0.1867 | LR 3.400066e-05
[2025-08-03 19:48:29,858::train::INFO] [Train] Iter 000900 | Loss 0.088903 | Grad Norm 0.3164 | LR 3.600064e-05
[2025-08-03 19:48:35,487::train::INFO] [Train] Iter 000950 | Loss 0.224762 | Grad Norm 0.2120 | LR 3.800062e-05
[2025-08-03 19:48:41,358::train::INFO] Norms - latent_z: 6.4330, label_vec: 2.0877
[2025-08-03 19:48:41,403::train::INFO] [Train] Iter 001000 | Loss 0.105698 | Grad Norm 0.1709 | LR 4.000060e-05
[2025-08-03 19:48:47,691::train::INFO] [Train] Iter 001050 | Loss 0.015037 | Grad Norm 0.1062 | LR 4.200058e-05
[2025-08-03 19:48:53,537::train::INFO] [Train] Iter 001100 | Loss 0.214720 | Grad Norm 0.4062 | LR 4.400056e-05
[2025-08-03 19:48:59,318::train::INFO] [Train] Iter 001150 | Loss 0.073069 | Grad Norm 0.3057 | LR 4.600054e-05
[2025-08-03 19:49:05,346::train::INFO] Norms - latent_z: 6.3887, label_vec: 2.5556
[2025-08-03 19:49:05,384::train::INFO] [Train] Iter 001200 | Loss 0.039227 | Grad Norm 0.1509 | LR 4.800052e-05
[2025-08-03 19:49:11,572::train::INFO] [Train] Iter 001250 | Loss 0.186060 | Grad Norm 0.1534 | LR 5.000050e-05
[2025-08-03 19:49:17,191::train::INFO] [Train] Iter 001300 | Loss 0.056576 | Grad Norm 0.2586 | LR 5.200048e-05
[2025-08-03 19:49:22,945::train::INFO] [Train] Iter 001350 | Loss 0.107198 | Grad Norm 0.1670 | LR 5.400046e-05
[2025-08-03 19:49:28,681::train::INFO] Norms - latent_z: 5.6041, label_vec: 2.5556
[2025-08-03 19:49:28,716::train::INFO] [Train] Iter 001400 | Loss 0.094569 | Grad Norm 0.1655 | LR 5.600044e-05
[2025-08-03 19:49:34,500::train::INFO] [Train] Iter 001450 | Loss 0.067897 | Grad Norm 0.2243 | LR 5.800042e-05
[2025-08-03 19:49:40,308::train::INFO] [Train] Iter 001500 | Loss 0.164338 | Grad Norm 0.3252 | LR 6.000040e-05
[2025-08-03 19:49:46,101::train::INFO] [Train] Iter 001550 | Loss 0.081413 | Grad Norm 0.1963 | LR 6.200038e-05
[2025-08-03 19:49:51,775::train::INFO] Norms - latent_z: 6.2004, label_vec: 2.3996
[2025-08-03 19:49:51,813::train::INFO] [Train] Iter 001600 | Loss 0.045205 | Grad Norm 0.1916 | LR 6.400036e-05
[2025-08-03 19:49:57,792::train::INFO] [Train] Iter 001650 | Loss 0.146067 | Grad Norm 0.2349 | LR 6.600034e-05
[2025-08-03 19:50:03,504::train::INFO] [Train] Iter 001700 | Loss 0.058228 | Grad Norm 0.3158 | LR 6.800032e-05
[2025-08-03 19:50:09,443::train::INFO] [Train] Iter 001750 | Loss 0.099947 | Grad Norm 0.1485 | LR 7.000030e-05
[2025-08-03 19:50:15,346::train::INFO] Norms - latent_z: 6.4931, label_vec: 2.3995
[2025-08-03 19:50:15,386::train::INFO] [Train] Iter 001800 | Loss 0.139286 | Grad Norm 0.2726 | LR 7.200028e-05
[2025-08-03 19:50:21,657::train::INFO] [Train] Iter 001850 | Loss 0.171450 | Grad Norm 0.2014 | LR 7.400026e-05
[2025-08-03 19:50:27,388::train::INFO] [Train] Iter 001900 | Loss 0.095894 | Grad Norm 0.2139 | LR 7.600024e-05
[2025-08-03 19:50:33,293::train::INFO] [Train] Iter 001950 | Loss 0.155294 | Grad Norm 0.1799 | LR 7.800022e-05
[2025-08-03 19:50:39,012::train::INFO] Norms - latent_z: 6.2039, label_vec: 2.2434
[2025-08-03 19:50:39,050::train::INFO] [Train] Iter 002000 | Loss 0.200155 | Grad Norm 0.3312 | LR 8.000020e-05
[2025-08-03 19:50:50,385::train::INFO] [Val] Iter 002000 | Avg Loss 0.075342 | CD(norm) 1195.193115 | EMD(norm) 5013872640.000000
[2025-08-03 19:50:50,587::train::INFO] New best validation metric (CD): 1195.193115 at iteration 2000. Saving best model.
[2025-08-03 19:50:57,541::train::INFO] [Train] Iter 002050 | Loss 0.122965 | Grad Norm 0.3152 | LR 8.200018e-05
[2025-08-03 19:51:03,533::train::INFO] [Train] Iter 002100 | Loss 0.154438 | Grad Norm 0.2165 | LR 8.400016e-05
[2025-08-03 19:51:09,413::train::INFO] [Train] Iter 002150 | Loss 0.310727 | Grad Norm 0.1529 | LR 8.600014e-05
[2025-08-03 19:51:15,035::train::INFO] Norms - latent_z: 6.2043, label_vec: 2.5550
[2025-08-03 19:51:15,076::train::INFO] [Train] Iter 002200 | Loss 0.126304 | Grad Norm 0.1298 | LR 8.800012e-05
[2025-08-03 19:51:21,092::train::INFO] [Train] Iter 002250 | Loss 0.112692 | Grad Norm 0.1415 | LR 9.000010e-05
[2025-08-03 19:51:27,075::train::INFO] [Train] Iter 002300 | Loss 0.017524 | Grad Norm 0.2817 | LR 9.200008e-05
[2025-08-03 19:51:32,655::train::INFO] [Train] Iter 002350 | Loss 0.263747 | Grad Norm 0.2778 | LR 9.400006e-05
[2025-08-03 19:51:38,164::train::INFO] Norms - latent_z: 6.5351, label_vec: 2.0872
[2025-08-03 19:51:38,205::train::INFO] [Train] Iter 002400 | Loss 0.228696 | Grad Norm 0.1574 | LR 9.600004e-05
[2025-08-03 19:51:44,156::train::INFO] [Train] Iter 002450 | Loss 0.178858 | Grad Norm 0.1649 | LR 9.800002e-05
[2025-08-03 19:51:49,697::train::INFO] [Train] Iter 002500 | Loss 0.203773 | Grad Norm 0.1304 | LR 1.000000e-04
[2025-08-03 19:51:55,239::train::INFO] [Train] Iter 002550 | Loss 0.065936 | Grad Norm 0.1507 | LR 9.999973e-05
[2025-08-03 19:52:00,873::train::INFO] Norms - latent_z: 6.4932, label_vec: 2.2429
[2025-08-03 19:52:00,915::train::INFO] [Train] Iter 002600 | Loss 0.080011 | Grad Norm 0.2499 | LR 9.999892e-05
[2025-08-03 19:52:06,945::train::INFO] [Train] Iter 002650 | Loss 0.172674 | Grad Norm 0.1650 | LR 9.999756e-05
[2025-08-03 19:52:12,609::train::INFO] [Train] Iter 002700 | Loss 0.149585 | Grad Norm 0.1214 | LR 9.999567e-05
[2025-08-03 19:52:18,357::train::INFO] [Train] Iter 002750 | Loss 0.113265 | Grad Norm 0.1438 | LR 9.999323e-05
[2025-08-03 19:52:24,306::train::INFO] Norms - latent_z: 6.4934, label_vec: 2.7106
[2025-08-03 19:52:24,350::train::INFO] [Train] Iter 002800 | Loss 0.192463 | Grad Norm 0.1205 | LR 9.999026e-05
[2025-08-03 19:52:30,296::train::INFO] [Train] Iter 002850 | Loss 0.144643 | Grad Norm 0.2206 | LR 9.998674e-05
[2025-08-03 19:52:35,974::train::INFO] [Train] Iter 002900 | Loss 0.118151 | Grad Norm 0.2833 | LR 9.998268e-05
[2025-08-03 19:52:41,661::train::INFO] [Train] Iter 002950 | Loss 0.129304 | Grad Norm 0.2857 | LR 9.997808e-05
[2025-08-03 19:52:47,310::train::INFO] Norms - latent_z: 5.6162, label_vec: 2.5545
[2025-08-03 19:52:47,346::train::INFO] [Train] Iter 003000 | Loss 0.203298 | Grad Norm 0.2277 | LR 9.997294e-05
[2025-08-03 19:52:53,627::train::INFO] [Train] Iter 003050 | Loss 0.150169 | Grad Norm 0.0999 | LR 9.996725e-05
[2025-08-03 19:52:59,281::train::INFO] [Train] Iter 003100 | Loss 0.179018 | Grad Norm 0.2292 | LR 9.996103e-05
[2025-08-03 19:53:05,043::train::INFO] [Train] Iter 003150 | Loss 0.115835 | Grad Norm 0.1261 | LR 9.995427e-05
[2025-08-03 19:53:10,822::train::INFO] Norms - latent_z: 5.4103, label_vec: 2.2426
[2025-08-03 19:53:10,856::train::INFO] [Train] Iter 003200 | Loss 0.161255 | Grad Norm 0.2226 | LR 9.994696e-05
[2025-08-03 19:53:17,046::train::INFO] [Train] Iter 003250 | Loss 0.227117 | Grad Norm 0.1651 | LR 9.993911e-05
[2025-08-03 19:53:22,807::train::INFO] [Train] Iter 003300 | Loss 0.035056 | Grad Norm 0.1534 | LR 9.993073e-05
[2025-08-03 19:53:28,812::train::INFO] [Train] Iter 003350 | Loss 0.125082 | Grad Norm 0.1819 | LR 9.992180e-05
[2025-08-03 19:53:35,017::train::INFO] Norms - latent_z: 5.6543, label_vec: 2.5542
[2025-08-03 19:53:35,070::train::INFO] [Train] Iter 003400 | Loss 0.028598 | Grad Norm 0.2949 | LR 9.991233e-05
[2025-08-03 19:53:41,260::train::INFO] [Train] Iter 003450 | Loss 0.141871 | Grad Norm 0.1388 | LR 9.990232e-05
[2025-08-03 19:53:47,410::train::INFO] [Train] Iter 003500 | Loss 0.009314 | Grad Norm 0.1049 | LR 9.989177e-05
[2025-08-03 19:53:53,301::train::INFO] [Train] Iter 003550 | Loss 0.103441 | Grad Norm 0.1408 | LR 9.988069e-05
[2025-08-03 19:53:59,211::train::INFO] Norms - latent_z: 6.2245, label_vec: 2.5541
[2025-08-03 19:53:59,249::train::INFO] [Train] Iter 003600 | Loss 0.221289 | Grad Norm 0.1543 | LR 9.986906e-05
[2025-08-03 19:54:05,593::train::INFO] [Train] Iter 003650 | Loss 0.157419 | Grad Norm 0.1126 | LR 9.985689e-05
[2025-08-03 19:54:11,350::train::INFO] [Train] Iter 003700 | Loss 0.066400 | Grad Norm 0.1336 | LR 9.984418e-05
[2025-08-03 19:54:17,225::train::INFO] [Train] Iter 003750 | Loss 0.189753 | Grad Norm 0.1758 | LR 9.983093e-05
[2025-08-03 19:54:22,769::train::INFO] Norms - latent_z: 5.6163, label_vec: 2.5539
[2025-08-03 19:54:22,805::train::INFO] [Train] Iter 003800 | Loss 0.089001 | Grad Norm 0.1040 | LR 9.981714e-05
[2025-08-03 19:54:28,700::train::INFO] [Train] Iter 003850 | Loss 0.108512 | Grad Norm 0.2479 | LR 9.980282e-05
[2025-08-03 19:54:34,137::train::INFO] [Train] Iter 003900 | Loss 0.198855 | Grad Norm 0.1513 | LR 9.978795e-05
[2025-08-03 19:54:39,798::train::INFO] [Train] Iter 003950 | Loss 0.167039 | Grad Norm 0.1633 | LR 9.977255e-05
[2025-08-03 19:54:45,629::train::INFO] Norms - latent_z: 6.2042, label_vec: 2.5537
[2025-08-03 19:54:45,664::train::INFO] [Train] Iter 004000 | Loss 0.150397 | Grad Norm 0.1304 | LR 9.975660e-05
[2025-08-03 19:54:56,990::train::INFO] [Val] Iter 004000 | Avg Loss 0.096230 | CD(norm) 179.454010 | EMD(norm) 752826624.000000
[2025-08-03 19:54:57,159::train::INFO] New best validation metric (CD): 179.454010 at iteration 4000. Saving best model.
[2025-08-03 19:55:03,629::train::INFO] [Train] Iter 004050 | Loss 0.138951 | Grad Norm 0.1791 | LR 9.974012e-05
[2025-08-03 19:55:09,645::train::INFO] [Train] Iter 004100 | Loss 0.127300 | Grad Norm 0.1524 | LR 9.972310e-05
[2025-08-03 19:55:15,077::train::INFO] [Train] Iter 004150 | Loss 0.171102 | Grad Norm 0.1367 | LR 9.970554e-05
[2025-08-03 19:55:20,559::train::INFO] Norms - latent_z: 6.3138, label_vec: 2.2417
[2025-08-03 19:55:20,594::train::INFO] [Train] Iter 004200 | Loss 0.197565 | Grad Norm 0.1546 | LR 9.968744e-05
[2025-08-03 19:55:26,328::train::INFO] [Train] Iter 004250 | Loss 0.078048 | Grad Norm 0.1281 | LR 9.966881e-05
[2025-08-03 19:55:32,020::train::INFO] [Train] Iter 004300 | Loss 0.022859 | Grad Norm 0.1447 | LR 9.964964e-05
[2025-08-03 19:55:37,512::train::INFO] [Train] Iter 004350 | Loss 0.179717 | Grad Norm 0.1229 | LR 9.962993e-05
[2025-08-03 19:55:42,867::train::INFO] Norms - latent_z: 6.5355, label_vec: 2.2413
[2025-08-03 19:55:42,906::train::INFO] [Train] Iter 004400 | Loss 0.113925 | Grad Norm 0.1805 | LR 9.960968e-05
[2025-08-03 19:55:48,863::train::INFO] [Train] Iter 004450 | Loss 0.163201 | Grad Norm 0.0901 | LR 9.958889e-05
[2025-08-03 19:55:54,342::train::INFO] [Train] Iter 004500 | Loss 0.054518 | Grad Norm 0.1023 | LR 9.956757e-05
[2025-08-03 19:55:59,853::train::INFO] [Train] Iter 004550 | Loss 0.068670 | Grad Norm 0.1694 | LR 9.954571e-05
[2025-08-03 19:56:05,335::train::INFO] Norms - latent_z: 6.2004, label_vec: 2.3968
[2025-08-03 19:56:05,368::train::INFO] [Train] Iter 004600 | Loss 0.218253 | Grad Norm 0.1814 | LR 9.952332e-05
[2025-08-03 19:56:11,107::train::INFO] [Train] Iter 004650 | Loss 0.056617 | Grad Norm 0.1334 | LR 9.950039e-05
[2025-08-03 19:56:16,624::train::INFO] [Train] Iter 004700 | Loss 0.151913 | Grad Norm 0.1177 | LR 9.947692e-05
[2025-08-03 19:56:22,075::train::INFO] [Train] Iter 004750 | Loss 0.091804 | Grad Norm 0.1534 | LR 9.945292e-05
[2025-08-03 19:56:27,488::train::INFO] Norms - latent_z: 5.5277, label_vec: 2.3966
[2025-08-03 19:56:27,523::train::INFO] [Train] Iter 004800 | Loss 0.050049 | Grad Norm 0.1176 | LR 9.942838e-05
[2025-08-03 19:56:33,345::train::INFO] [Train] Iter 004850 | Loss 0.192308 | Grad Norm 0.1692 | LR 9.940331e-05
[2025-08-03 19:56:38,747::train::INFO] [Train] Iter 004900 | Loss 0.198412 | Grad Norm 0.1261 | LR 9.937770e-05
[2025-08-03 19:56:44,003::train::INFO] [Train] Iter 004950 | Loss 0.190274 | Grad Norm 0.1406 | LR 9.935156e-05
[2025-08-03 19:56:49,180::train::INFO] Norms - latent_z: 6.7763, label_vec: 2.2406
[2025-08-03 19:56:49,215::train::INFO] [Train] Iter 005000 | Loss 0.092459 | Grad Norm 0.1791 | LR 9.932488e-05
[2025-08-03 19:56:55,070::train::INFO] [Train] Iter 005050 | Loss 0.124114 | Grad Norm 0.1879 | LR 9.929767e-05
[2025-08-03 19:57:00,464::train::INFO] [Train] Iter 005100 | Loss 0.199668 | Grad Norm 0.1289 | LR 9.926993e-05
[2025-08-03 19:57:05,975::train::INFO] [Train] Iter 005150 | Loss 0.149370 | Grad Norm 0.1365 | LR 9.924165e-05
[2025-08-03 19:57:11,419::train::INFO] Norms - latent_z: 6.7739, label_vec: 2.3960
[2025-08-03 19:57:11,457::train::INFO] [Train] Iter 005200 | Loss 0.025192 | Grad Norm 0.1533 | LR 9.921284e-05
[2025-08-03 19:57:17,337::train::INFO] [Train] Iter 005250 | Loss 0.274490 | Grad Norm 0.2540 | LR 9.918350e-05
[2025-08-03 19:57:22,811::train::INFO] [Train] Iter 005300 | Loss 0.226308 | Grad Norm 0.1575 | LR 9.915363e-05
[2025-08-03 19:57:28,260::train::INFO] [Train] Iter 005350 | Loss 0.201051 | Grad Norm 0.0833 | LR 9.912322e-05
[2025-08-03 19:57:33,736::train::INFO] Norms - latent_z: 6.4934, label_vec: 2.3955
[2025-08-03 19:57:33,774::train::INFO] [Train] Iter 005400 | Loss 0.157178 | Grad Norm 0.0923 | LR 9.909228e-05
[2025-08-03 19:57:39,638::train::INFO] [Train] Iter 005450 | Loss 0.064182 | Grad Norm 0.0903 | LR 9.906081e-05
[2025-08-03 19:57:45,126::train::INFO] [Train] Iter 005500 | Loss 0.140907 | Grad Norm 0.1503 | LR 9.902881e-05
[2025-08-03 19:57:50,709::train::INFO] [Train] Iter 005550 | Loss 0.066907 | Grad Norm 0.1007 | LR 9.899628e-05
[2025-08-03 19:57:56,134::train::INFO] Norms - latent_z: 6.4098, label_vec: 2.5511
[2025-08-03 19:57:56,180::train::INFO] [Train] Iter 005600 | Loss 0.190434 | Grad Norm 0.0927 | LR 9.896321e-05
[2025-08-03 19:58:02,091::train::INFO] [Train] Iter 005650 | Loss 0.187709 | Grad Norm 0.1065 | LR 9.892962e-05
[2025-08-03 19:58:07,749::train::INFO] [Train] Iter 005700 | Loss 0.126979 | Grad Norm 0.1202 | LR 9.889550e-05
[2025-08-03 19:58:13,304::train::INFO] [Train] Iter 005750 | Loss 0.100854 | Grad Norm 0.1063 | LR 9.886085e-05
[2025-08-03 19:58:18,898::train::INFO] Norms - latent_z: 6.2001, label_vec: 2.3947
[2025-08-03 19:58:18,932::train::INFO] [Train] Iter 005800 | Loss 0.100385 | Grad Norm 0.1047 | LR 9.882567e-05
[2025-08-03 19:58:24,846::train::INFO] [Train] Iter 005850 | Loss 0.152538 | Grad Norm 0.1586 | LR 9.878996e-05
[2025-08-03 19:58:30,309::train::INFO] [Train] Iter 005900 | Loss 0.091064 | Grad Norm 0.1709 | LR 9.875372e-05
[2025-08-03 19:58:35,803::train::INFO] [Train] Iter 005950 | Loss 0.142283 | Grad Norm 0.0930 | LR 9.871696e-05
[2025-08-03 19:58:41,171::train::INFO] Norms - latent_z: 6.2046, label_vec: 2.2386
[2025-08-03 19:58:41,206::train::INFO] [Train] Iter 006000 | Loss 0.112650 | Grad Norm 0.1326 | LR 9.867967e-05
[2025-08-03 19:58:52,595::train::INFO] [Val] Iter 006000 | Avg Loss 0.224145 | CD(norm) 109.985672 | EMD(norm) 461382944.000000
[2025-08-03 19:58:52,753::train::INFO] New best validation metric (CD): 109.985672 at iteration 6000. Saving best model.
[2025-08-03 19:58:58,911::train::INFO] [Train] Iter 006050 | Loss 0.147599 | Grad Norm 0.0977 | LR 9.864185e-05

[2025-07-28 19:44:47,167::train::INFO] Namespace(latent_dim=256, model_type='vae', resume=None, use_vae=True, kl_weight=0.001, num_steps=1000, beta_1=0.0001, beta_T=0.02, sched_mode='cosine', learn_sigma_diffusion=True, residual_within_block_diffusion=True, time_emb_dim_diffusion=128, lambda_vlb_diffusion=0.001, dataset_path='/home/<USER>/llm/3DAD/data/Real3D-AD-PCD', categories=['airplane'], scale_mode='shape_bbox', train_batch_size=4, val_batch_size=4, num_points=2048, sampling_method='hybrid', rotate=False, precomputed_defect_path='./defect_generations_strong', lr=0.0001, weight_decay=0.0, max_grad_norm=1.0, use_ema=True, ema_decay=0.9999, seed=2024, logging=True, log_root='./logs_ae_vae', device='cuda', max_iters=50000, val_freq=2000, tag=None, num_workers=4, warmup_iters=2500, num_val_batches=4, num_inspect_batches=1, num_inspect_pointclouds=2, ret_traj=False, sample_steps_decode=None, flexibility=0.0, metrics_in_normalized_space=True, pretrained_vae_path='/home/<USER>/llm/3DAD/diffusion-point-cloud/logs_vae/VAE_2025_07_28__13_32_48/model_best.pt', freeze_encoder_after=True, num_defect_types=2, label_emb_dim=8)
[2025-07-28 19:44:47,168::train::INFO] Loading datasets...
[2025-07-28 19:45:24,001::train::INFO] 训练集 (从预计算目录加载): 32 个样本
[2025-07-28 19:45:24,001::train::INFO] 验证集 (原始正常数据): 4 个样本
[2025-07-28 19:45:24,003::train::INFO] Building model...
[2025-07-28 19:45:24,216::train::ERROR] Failed to load pretrained VAE encoder: Weights only load failed. This file can still be loaded, to do so you have two options, [1mdo those steps only if you trust the source of the checkpoint[0m. 
	(1) In PyTorch 2.6, we changed the default value of the `weights_only` argument in `torch.load` from `False` to `True`. Re-running `torch.load` with `weights_only` set to `False` will likely succeed, but it can result in arbitrary code execution. Do it only if you got the file from a trusted source.
	(2) Alternatively, to load with `weights_only=True` please check the recommended steps in the following error message.
	WeightsUnpickler error: Unsupported global: GLOBAL argparse.Namespace was not an allowed global by default. Please use `torch.serialization.add_safe_globals([Namespace])` or the `torch.serialization.safe_globals([Namespace])` context manager to allowlist this global if you trust this class/function.

Check the documentation of torch.load to learn more about types accepted by default with weights_only https://pytorch.org/docs/stable/generated/torch.load.html.
[2025-07-28 19:45:24,217::train::INFO] AutoEncoder(
  (encoder): PointTransformerV3Encoder(
    (embedding): Sequential(
      (0): Conv1d(3, 128, kernel_size=(1,), stride=(1,))
      (1): BatchNorm1d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (2): ReLU()
      (3): Conv1d(128, 128, kernel_size=(1,), stride=(1,))
      (4): BatchNorm1d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (5): ReLU()
    )
    (sa1): TransformerBlock(
      (q_conv): Conv1d(128, 128, kernel_size=(1,), stride=(1,), bias=False)
      (k_conv): Conv1d(128, 128, kernel_size=(1,), stride=(1,), bias=False)
      (v_conv): Conv1d(128, 128, kernel_size=(1,), stride=(1,), bias=False)
      (attn_norm): BatchNorm1d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (ff): Sequential(
        (0): Conv1d(128, 256, kernel_size=(1,), stride=(1,))
        (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU()
        (3): Conv1d(256, 128, kernel_size=(1,), stride=(1,))
        (4): BatchNorm1d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      )
      (shortcut): Identity()
    )
    (sa2): TransformerBlock(
      (q_conv): Conv1d(128, 256, kernel_size=(1,), stride=(1,), bias=False)
      (k_conv): Conv1d(128, 256, kernel_size=(1,), stride=(1,), bias=False)
      (v_conv): Conv1d(128, 256, kernel_size=(1,), stride=(1,), bias=False)
      (attn_norm): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (ff): Sequential(
        (0): Conv1d(256, 512, kernel_size=(1,), stride=(1,))
        (1): BatchNorm1d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU()
        (3): Conv1d(512, 256, kernel_size=(1,), stride=(1,))
        (4): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      )
      (shortcut): Sequential(
        (0): Conv1d(128, 256, kernel_size=(1,), stride=(1,))
        (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      )
    )
    (sa3): TransformerBlock(
      (q_conv): Conv1d(256, 512, kernel_size=(1,), stride=(1,), bias=False)
      (k_conv): Conv1d(256, 512, kernel_size=(1,), stride=(1,), bias=False)
      (v_conv): Conv1d(256, 512, kernel_size=(1,), stride=(1,), bias=False)
      (attn_norm): BatchNorm1d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (ff): Sequential(
        (0): Conv1d(512, 1024, kernel_size=(1,), stride=(1,))
        (1): BatchNorm1d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU()
        (3): Conv1d(1024, 512, kernel_size=(1,), stride=(1,))
        (4): BatchNorm1d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      )
      (shortcut): Sequential(
        (0): Conv1d(256, 512, kernel_size=(1,), stride=(1,))
        (1): BatchNorm1d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      )
    )
    (global_conv): Sequential(
      (0): Conv1d(512, 1024, kernel_size=(1,), stride=(1,))
      (1): BatchNorm1d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (2): ReLU()
    )
    (fc_mean): Sequential(
      (0): Linear(in_features=1024, out_features=512, bias=True)
      (1): BatchNorm1d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (2): ReLU()
      (3): Linear(in_features=512, out_features=256, bias=True)
      (4): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (5): ReLU()
      (6): Linear(in_features=256, out_features=256, bias=True)
    )
    (fc_var): Sequential(
      (0): Linear(in_features=1024, out_features=512, bias=True)
      (1): BatchNorm1d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (2): ReLU()
      (3): Linear(in_features=512, out_features=256, bias=True)
      (4): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (5): ReLU()
      (6): Linear(in_features=256, out_features=256, bias=True)
    )
  )
  (label_emb): Embedding(2, 8)
  (diffusion_decoder): DiffusionPoint(
    (net): PointwiseNet(
      (act): SiLU()
      (time_mlp): SinusoidalPositionEmbeddings()
      (point_embedding): Sequential(
        (0): Linear(in_features=3, out_features=64, bias=True)
        (1): LayerNorm((64,), eps=1e-05, elementwise_affine=True)
        (2): SiLU()
        (3): Linear(in_features=64, out_features=128, bias=True)
        (4): LayerNorm((128,), eps=1e-05, elementwise_affine=True)
        (5): SiLU()
      )
      (context_embedding_mlp): Sequential(
        (0): Linear(in_features=392, out_features=128, bias=True)
        (1): LayerNorm((128,), eps=1e-05, elementwise_affine=True)
        (2): SiLU()
        (3): Linear(in_features=128, out_features=128, bias=True)
        (4): LayerNorm((128,), eps=1e-05, elementwise_affine=True)
        (5): SiLU()
      )
      (local_attention_blocks): ModuleList(
        (0-1): 2 x SelfAttentionBlock(
          (norm): LayerNorm((128,), eps=1e-05, elementwise_affine=True)
          (attention): MultiheadAttention(
            (out_proj): NonDynamicallyQuantizableLinear(in_features=128, out_features=128, bias=True)
          )
          (ff): Sequential(
            (0): Linear(in_features=128, out_features=512, bias=True)
            (1): GELU(approximate='none')
            (2): Linear(in_features=512, out_features=128, bias=True)
          )
          (ff_norm): LayerNorm((128,), eps=1e-05, elementwise_affine=True)
        )
      )
      (global_attention_blocks): ModuleList(
        (0-1): 2 x CrossAttentionBlock(
          (norm_q): LayerNorm((128,), eps=1e-05, elementwise_affine=True)
          (norm_kv): LayerNorm((128,), eps=1e-05, elementwise_affine=True)
          (attention): MultiheadAttention(
            (out_proj): NonDynamicallyQuantizableLinear(in_features=128, out_features=128, bias=True)
          )
          (ff): Sequential(
            (0): Linear(in_features=128, out_features=512, bias=True)
            (1): GELU(approximate='none')
            (2): Linear(in_features=512, out_features=128, bias=True)
          )
          (ff_norm): LayerNorm((128,), eps=1e-05, elementwise_affine=True)
        )
      )
      (output_layers): Sequential(
        (0): Linear(in_features=128, out_features=128, bias=True)
        (1): LayerNorm((128,), eps=1e-05, elementwise_affine=True)
        (2): SiLU()
        (3): Linear(in_features=128, out_features=64, bias=True)
        (4): LayerNorm((64,), eps=1e-05, elementwise_affine=True)
        (5): SiLU()
        (6): Linear(in_features=64, out_features=6, bias=True)
      )
    )
    (var_sched): VarianceSchedule()
  )
)
[2025-07-28 19:45:24,221::train::INFO] EMA initialized with decay=0.9999
[2025-07-28 19:45:24,221::train::INFO] Encoder frozen as per flag.
[2025-07-28 19:45:24,222::train::INFO] Setting up learning rate schedule: 2500 warmup iters, then cosine decay.
[2025-07-28 19:45:24,223::train::INFO] Start training...
[2025-07-28 19:45:28,998::train::INFO] [Train] Iter 000050 | Loss 1.004893 | Grad Norm 5.4110 | LR 2.000980e-06
[2025-07-28 19:45:32,938::train::INFO] [Train] Iter 000100 | Loss 0.746989 | Grad Norm 2.9770 | LR 4.000960e-06
[2025-07-28 19:45:36,752::train::INFO] [Train] Iter 000150 | Loss 0.540292 | Grad Norm 2.2741 | LR 6.000940e-06
[2025-07-28 19:45:40,603::train::INFO] [Train] Iter 000200 | Loss 0.464864 | Grad Norm 1.9143 | LR 8.000920e-06
[2025-07-28 19:45:44,769::train::INFO] [Train] Iter 000250 | Loss 0.296481 | Grad Norm 0.9818 | LR 1.000090e-05
[2025-07-28 19:45:48,705::train::INFO] [Train] Iter 000300 | Loss 0.272757 | Grad Norm 0.8273 | LR 1.200088e-05
[2025-07-28 19:45:52,591::train::INFO] [Train] Iter 000350 | Loss 0.263689 | Grad Norm 0.7030 | LR 1.400086e-05
[2025-07-28 19:45:56,267::train::INFO] [Train] Iter 000400 | Loss 0.151158 | Grad Norm 0.7033 | LR 1.600084e-05
[2025-07-28 19:46:00,205::train::INFO] [Train] Iter 000450 | Loss 0.192344 | Grad Norm 0.5287 | LR 1.800082e-05
[2025-07-28 19:46:04,011::train::INFO] [Train] Iter 000500 | Loss 0.115964 | Grad Norm 0.4845 | LR 2.000080e-05
[2025-07-28 19:46:07,941::train::INFO] [Train] Iter 000550 | Loss 0.125101 | Grad Norm 0.3255 | LR 2.200078e-05
[2025-07-28 19:46:11,886::train::INFO] [Train] Iter 000600 | Loss 0.167088 | Grad Norm 0.2891 | LR 2.400076e-05
[2025-07-28 19:46:16,036::train::INFO] [Train] Iter 000650 | Loss 0.296523 | Grad Norm 0.3784 | LR 2.600074e-05
[2025-07-28 19:46:19,883::train::INFO] [Train] Iter 000700 | Loss 0.188832 | Grad Norm 0.5212 | LR 2.800072e-05
[2025-07-28 19:46:23,697::train::INFO] [Train] Iter 000750 | Loss 0.193804 | Grad Norm 0.2544 | LR 3.000070e-05
[2025-07-28 19:46:27,576::train::INFO] [Train] Iter 000800 | Loss 0.225032 | Grad Norm 0.2382 | LR 3.200068e-05
[2025-07-28 19:46:31,687::train::INFO] [Train] Iter 000850 | Loss 0.169975 | Grad Norm 0.4856 | LR 3.400066e-05
[2025-07-28 19:46:35,516::train::INFO] [Train] Iter 000900 | Loss 0.090277 | Grad Norm 0.2706 | LR 3.600064e-05
[2025-07-28 19:46:39,438::train::INFO] [Train] Iter 000950 | Loss 0.227434 | Grad Norm 0.1700 | LR 3.800062e-05
[2025-07-28 19:46:43,328::train::INFO] [Train] Iter 001000 | Loss 0.106103 | Grad Norm 0.1354 | LR 4.000060e-05
[2025-07-28 19:46:47,476::train::INFO] [Train] Iter 001050 | Loss 0.015250 | Grad Norm 0.1344 | LR 4.200058e-05
[2025-07-28 19:46:51,256::train::INFO] [Train] Iter 001100 | Loss 0.213808 | Grad Norm 0.3304 | LR 4.400056e-05
[2025-07-28 19:46:55,175::train::INFO] [Train] Iter 001150 | Loss 0.076867 | Grad Norm 0.2500 | LR 4.600054e-05
[2025-07-28 19:46:59,051::train::INFO] [Train] Iter 001200 | Loss 0.042110 | Grad Norm 0.1483 | LR 4.800052e-05
[2025-07-28 19:47:03,131::train::INFO] [Train] Iter 001250 | Loss 0.181323 | Grad Norm 0.4050 | LR 5.000050e-05
[2025-07-28 19:47:07,050::train::INFO] [Train] Iter 001300 | Loss 0.059999 | Grad Norm 0.3205 | LR 5.200048e-05
[2025-07-28 19:47:10,865::train::INFO] [Train] Iter 001350 | Loss 0.105797 | Grad Norm 0.3028 | LR 5.400046e-05
[2025-07-28 19:47:14,682::train::INFO] [Train] Iter 001400 | Loss 0.094480 | Grad Norm 0.2984 | LR 5.600044e-05
[2025-07-28 19:47:18,815::train::INFO] [Train] Iter 001450 | Loss 0.069410 | Grad Norm 0.2804 | LR 5.800042e-05
[2025-07-28 19:47:22,704::train::INFO] [Train] Iter 001500 | Loss 0.159948 | Grad Norm 0.1409 | LR 6.000040e-05
[2025-07-28 19:47:26,561::train::INFO] [Train] Iter 001550 | Loss 0.079395 | Grad Norm 0.2020 | LR 6.200038e-05
[2025-07-28 19:47:30,372::train::INFO] [Train] Iter 001600 | Loss 0.046025 | Grad Norm 0.3364 | LR 6.400036e-05
[2025-07-28 19:47:34,578::train::INFO] [Train] Iter 001650 | Loss 0.139515 | Grad Norm 0.2353 | LR 6.600034e-05
[2025-07-28 19:47:38,547::train::INFO] [Train] Iter 001700 | Loss 0.056644 | Grad Norm 0.2289 | LR 6.800032e-05
[2025-07-28 19:47:42,493::train::INFO] [Train] Iter 001750 | Loss 0.099693 | Grad Norm 0.1782 | LR 7.000030e-05
[2025-07-28 19:47:46,329::train::INFO] [Train] Iter 001800 | Loss 0.146625 | Grad Norm 0.2079 | LR 7.200028e-05
[2025-07-28 19:47:50,431::train::INFO] [Train] Iter 001850 | Loss 0.177736 | Grad Norm 0.4481 | LR 7.400026e-05
[2025-07-28 19:47:54,307::train::INFO] [Train] Iter 001900 | Loss 0.095622 | Grad Norm 0.2088 | LR 7.600024e-05
[2025-07-28 19:47:58,242::train::INFO] [Train] Iter 001950 | Loss 0.145827 | Grad Norm 0.1984 | LR 7.800022e-05
[2025-07-28 19:48:02,121::train::INFO] [Train] Iter 002000 | Loss 0.206819 | Grad Norm 0.2541 | LR 8.000020e-05
[2025-07-28 19:48:10,530::train::ERROR] An error occurred during training: Expected all tensors to be on the same device, but found at least two devices, cuda:0 and cpu!
Traceback (most recent call last):
  File "/home/<USER>/llm/3DAD/diffusion-point-cloud/train_ae.py", line 459, in <module>
    current_val_metric = validate_loss(it)
                         ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/llm/3DAD/diffusion-point-cloud/train_ae.py", line 410, in validate_loss
    first_batch_refs = (ref_norm * scale + shift).cpu()
                        ~~~~~~~~~^~~~~~~
RuntimeError: Expected all tensors to be on the same device, but found at least two devices, cuda:0 and cpu!
[2025-07-28 19:48:10,596::train::INFO] Training finished or terminated.

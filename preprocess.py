import os
import argparse
import torch
from tqdm.auto import tqdm
from torch.utils.data import DataLoader

# 确保可以从你的项目中正确导入这些模块
from utils.dataset import Real3DADDataset, PrecomputedDefectDataset
from utils.data import custom_collate_fn
from utils.misc import seed_all, str_list

def main(args):
    """
    执行离线数据预处理。
    这个脚本会加载原始数据，应用所有变换（归一化、对齐、采样），
    然后将最终的张量字典保存到磁盘，以供训练时快速加载。
    """
    seed_all(42)
    os.makedirs(args.output_path, exist_ok=True)
    print(f"预处理后的数据将保存到: {args.output_path}")

    # --- 1. 准备原始数据源 ---
    # 这个Dataset实例只用于加载原始、未处理的点云文件
    raw_data_provider = Real3DADDataset(
        path=args.dataset_path,
        categories=args.categories,
        split='train',
        scale_mode=None,
        transform=None,
        num_points=None,
        sampling_method='none'
    )

    # --- 2. 使用我们之前设计的“慢速但正确”的Dataset来进行处理 ---
    # 我们将 use_augmentation 设为 False，以生成一个确定性的、干净的数据集。
    # 随机增强可以在训练时通过 transform 实时应用。
    processing_dataset = PrecomputedDefectDataset(
        root_dir=args.precomputed_defect_path,
        raw_data_source=raw_data_provider,
        scale_mode=args.scale_mode,
        num_points=args.num_points,
        sampling_method=args.sampling_method,
        use_augmentation=False  # 预处理时不使用随机增强
    )

    # --- 3. 使用 DataLoader 遍历并处理所有数据 ---
    # num_workers > 0 可以利用多核CPU加速处理
    loader = DataLoader(
        processing_dataset,
        batch_size=args.batch_size,
        shuffle=False, # 不打乱，按顺序处理
        num_workers=args.num_workers,
        collate_fn=custom_collate_fn
    )

    print("开始处理数据...")
    count = 0
    for batch in tqdm(loader, desc="正在预处理数据"):
        if batch is None:
            continue

        # Dataloader已经将样本打包成批次，我们需要解包并单独保存
        batch_size = batch['pointcloud_norm'].size(0)
        for i in range(batch_size):
            # 为每个样本创建一个字典
            sample_data = {
                'pointcloud_anom': batch['pointcloud_anom'][i],
                'pointcloud_norm': batch['pointcloud_norm'][i],
                'defect_label': batch['defect_label'][i],
                'original_filename': batch['original_filename'][i]
            }

            # 定义保存路径和文件名
            # 例如: .../processed_data/airplane/60_template_bulge_0.pt
            original_fname_base = os.path.splitext(sample_data['original_filename'])[0]
            
            # 从原始文件名和缺陷类型构造新文件名
            defect_type = 'bulge' if sample_data['defect_label'].item() == 1 else 'sink'
            # 这是一个简化的命名，你可能需要从anom_path解析出更精确的名字
            # 为了简单起见，我们使用一个唯一的计数器
            save_fname = f"{original_fname_base}_{defect_type}_{count}.pt"
            
            # 创建类别子目录
            category = args.categories[0] # 简化假设，你可能需要从文件名解析
            category_dir = os.path.join(args.output_path, category)
            os.makedirs(category_dir, exist_ok=True)
            
            save_path = os.path.join(category_dir, save_fname)
            
            # 保存为.pt文件
            torch.save(sample_data, save_path)
            count += 1
            
    print(f"预处理完成！总共保存了 {count} 个样本。")


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="离线数据预处理脚本")
    # --- 输入路径 ---
    parser.add_argument('--dataset_path', type=str, default='/home/<USER>/llm/3DAD/data/Real3D-AD-PCD')
    parser.add_argument('--precomputed_defect_path', type=str, default="./defect_generations_strong")
    # --- 输出路径 ---
    parser.add_argument('--output_path', type=str, default='./processed_data', help="保存.pt文件的根目录")
    # --- 数据参数 (必须与你的训练配置一致!) ---
    parser.add_argument('--categories', type=str_list, default=['airplane'])
    parser.add_argument('--scale_mode', type=str, default='shape_bbox')
    parser.add_argument('--num_points', type=int, default=2048)
    parser.add_argument('--sampling_method', type=str, default='hybrid')
    # --- 处理参数 ---
    parser.add_argument('--batch_size', type=int, default=4, help="用于预处理的批次大小，可以设大一点以加快速度")
    parser.add_argument('--num_workers', type=int, default=4, help="用于预处理的CPU核心数")
    
    args = parser.parse_args()
    main(args)